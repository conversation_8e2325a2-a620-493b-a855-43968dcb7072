{"cells": [{"cell_type": "code", "execution_count": 1, "id": "102c51e1-d73a-4e36-ae53-d4473ddbfdd5", "metadata": {}, "outputs": [], "source": ["# pip install numpy"]}, {"cell_type": "code", "execution_count": 2, "id": "b3c1c9ac-b5b1-4313-86c0-1d8eda9958d8", "metadata": {}, "outputs": [], "source": ["import numpy as np"]}, {"cell_type": "code", "execution_count": 3, "id": "74131e2a-4647-4f73-a68f-627fe22c75f7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2.3.3\n"]}], "source": ["print(np.__version__)"]}, {"cell_type": "code", "execution_count": 4, "id": "bb6c8160-1099-4430-b421-744c4cee15f5", "metadata": {}, "outputs": [], "source": ["# ndarray"]}, {"cell_type": "code", "execution_count": 5, "id": "fdd24d62-65b0-468f-a35d-0572f35c6601", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["25\n"]}], "source": ["arr = np.array(25)\n", "print(arr)"]}, {"cell_type": "code", "execution_count": 6, "id": "e5b67c87-a5fc-4f2b-b74c-f9fa44fb5532", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0\n"]}], "source": ["print(arr.ndim)"]}, {"cell_type": "code", "execution_count": 7, "id": "2742eefb-8738-4ca9-843e-87be31eecf1e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["25\n"]}], "source": ["arr1 = np.array([1,2,3,4,5])\n", "print(arr)"]}, {"cell_type": "code", "execution_count": 8, "id": "daf1e54f-b00b-4bf5-9144-3cb966386e72", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1\n"]}], "source": ["print(arr1.ndim)"]}, {"cell_type": "code", "execution_count": 9, "id": "1b208cdf-3e74-43ad-96fb-55659b735787", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[ 1  2  3  4  5]\n", " [ 6  7  8  9 10]]\n"]}], "source": ["arr2 = np.array([[1,2,3,4,5] , [6,7,8,9,10]])\n", "print(arr2)"]}, {"cell_type": "code", "execution_count": 10, "id": "298709c7-0999-487a-88a4-b70018b5a6bd", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0\n"]}], "source": ["print(arr.ndim)"]}, {"cell_type": "code", "execution_count": 11, "id": "356c3afd-c9f5-4f82-97e5-54fd5f9fa2d7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[[ 1  2  3  4  5]\n", "  [ 6  7  8  9 10]]\n", "\n", " [[11 12 13 14 15]\n", "  [16 17 18 19 20]]]\n"]}], "source": ["arr3 = np.array([ [ [1,2,3,4,5] , [6,7,8,9,10] ], [[11,12,13,14,15] , [16,17,18,19,20] ] ])\n", "print(arr3)"]}, {"cell_type": "code", "execution_count": 12, "id": "fa531a04-6ae4-4ea6-a4e9-bc2603942e67", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["3\n"]}], "source": ["print(arr3.ndim)"]}, {"cell_type": "code", "execution_count": 13, "id": "5e92a28f-7678-456e-af34-9d9ef64c0149", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[[[[[[[1 2 3 4 5]]]]]]]]\n"]}], "source": ["arr8 = np.array([1,2,3,4,5] , ndmin=8)\n", "print(arr8)"]}, {"cell_type": "code", "execution_count": 14, "id": "1f3dd6aa-72e5-47b5-a703-2fe00754ebf3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0\n"]}], "source": ["print(arr.ndim)"]}, {"cell_type": "code", "execution_count": 15, "id": "e06628eb-3352-4f47-a842-422ac2157f3d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[1 2 3 4 5]\n", "1\n", "4\n", "4\n"]}], "source": ["print(arr1)\n", "print(arr1[0])\n", "print(arr1[3])\n", "print(arr1[-2])"]}, {"cell_type": "code", "execution_count": 16, "id": "58b7c696-247d-421c-b579-fd4d25fef5fc", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[ 1  2  3  4  5]\n", " [ 6  7  8  9 10]]\n", "3\n", "10\n"]}], "source": ["print(arr2)\n", "print(arr2[0,2])\n", "print(arr2[1,4])"]}, {"cell_type": "code", "execution_count": 17, "id": "ea85acbc-f9e3-439e-9d4c-ef6244131cd6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[[ 1  2  3  4  5]\n", "  [ 6  7  8  9 10]]\n", "\n", " [[11 12 13 14 15]\n", "  [16 17 18 19 20]]]\n", "7\n", "18\n", "14\n"]}], "source": ["print(arr3)\n", "print(arr3[0,1,1])\n", "print(arr3[1,1,2])\n", "print(arr3[1,0,3])"]}, {"cell_type": "code", "execution_count": 18, "id": "085ae85e-d0d3-4ab1-947c-e675b18f5ec4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[1 2 3 4 5]\n", "[2 3 4]\n", "[2 3 4 5]\n", "[1 2 3 4]\n", "[3 4]\n"]}], "source": ["# start : end : step\n", "print(arr1)\n", "print(arr1[1:4])\n", "print(arr1[1:])\n", "print(arr1[:4])\n", "print(arr1[-3:-1])"]}, {"cell_type": "code", "execution_count": 19, "id": "0f56ba3e-2741-4a3e-9079-ead6dc545f4e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[2 3 4]\n", "[1 3]\n"]}], "source": ["print(arr1[1:4:1])\n", "print(arr1[0:4:2])"]}, {"cell_type": "code", "execution_count": 20, "id": "6642ef81-c061-4d6f-937a-2686f10f575e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[1 2 3 4 5]\n"]}], "source": ["print(arr1[::])"]}, {"cell_type": "code", "execution_count": 21, "id": "d4e20a5e-cf5d-4767-b2c3-1a582cf280d3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[ 1  2  3  4  5]\n", " [ 6  7  8  9 10]]\n"]}], "source": ["print(arr2)"]}, {"cell_type": "code", "execution_count": 22, "id": "4504227a-5390-4df0-9a26-d4face8d6ce4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[3 4]\n", " [8 9]]\n"]}], "source": ["print(arr2[0:2 , 2:4 ])"]}, {"cell_type": "code", "execution_count": 23, "id": "0169d118-81e1-4fdc-8ed7-659adf13e5ba", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[ 5]\n", " [10]]\n"]}], "source": ["print(arr2[0:2 , 4:5 ])"]}, {"cell_type": "code", "execution_count": 24, "id": "d7af0d8e-2e78-4e1a-9ae6-d61d14d96959", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[1 2 3 4 5]\n"]}], "source": ["new = np.array([1,2,3,4,5])\n", "print(new)"]}, {"cell_type": "code", "execution_count": 25, "id": "922bee9b-5b0b-4fcb-8260-7ef651eb6868", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[1 2 3 4 5]\n", "[1 2 3 4 5]\n"]}], "source": ["copy = new.copy()\n", "view = new.view()\n", "print(copy)\n", "print(view)"]}, {"cell_type": "code", "execution_count": 26, "id": "a6dba2c3-5588-4bdb-a0a9-b3bfd35956ee", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[1 2 3 4 5]\n", "[45  2  3  4  5]\n"]}], "source": ["new[0] = 45\n", "print(copy)\n", "print(view)"]}, {"cell_type": "code", "execution_count": 27, "id": "a59b5e11-27f5-436a-800d-603156381a5b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["None\n", "[45  2  3  4  5]\n"]}], "source": ["print(copy.base)\n", "print(view.base)"]}, {"cell_type": "code", "execution_count": 28, "id": "c49f4066-a799-4bb6-a12e-5fe0b2f1ca68", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(5,)\n"]}], "source": ["print(arr1.shape)"]}, {"cell_type": "code", "execution_count": 29, "id": "aa293790-b37e-44bf-8f22-f3edef59b810", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(2, 5)\n"]}], "source": ["print(arr2.shape)"]}, {"cell_type": "code", "execution_count": 30, "id": "8a547add-6ed2-4694-8525-bd751b0538da", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(2, 2, 5)\n"]}], "source": ["print(arr3.shape)"]}, {"cell_type": "code", "execution_count": 31, "id": "ddcbc307-55ce-46ad-a35f-8edf4b855ede", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[ 1  2  3  4  5  6  7  8  9 10 11 12]\n", "--------------\n", "[[ 1  2  3]\n", " [ 4  5  6]\n", " [ 7  8  9]\n", " [10 11 12]]\n", "--------------\n", "[[[ 1  2  3]\n", "  [ 4  5  6]]\n", "\n", " [[ 7  8  9]\n", "  [10 11 12]]]\n"]}], "source": ["arr1d = np.array([1,2,3,4,5,6,7,8,9,10,11, 12])\n", "arr2d = arr1d.reshape(4,3)\n", "arr3d = arr1d.reshape(2,2,3)\n", "print(arr1d)\n", "print(\"--------------\")\n", "print(arr2d)\n", "print(\"--------------\")\n", "print(arr3d)"]}, {"cell_type": "code", "execution_count": 32, "id": "13d4793f-c03e-4027-b2b9-e31f5623396e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1\n", "2\n", "3\n", "4\n", "5\n"]}], "source": ["for x in arr1:\n", "    print(x)"]}, {"cell_type": "code", "execution_count": 33, "id": "4b8f0787-0443-4f43-a595-81bb6463579f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1\n", "2\n", "3\n", "4\n", "5\n", "6\n", "7\n", "8\n", "9\n", "10\n"]}], "source": ["for x in arr2:\n", "    for y in x:\n", "        print(y)"]}, {"cell_type": "code", "execution_count": 34, "id": "888b7650-4214-41c5-a65a-cd61d4bbf111", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1\n", "2\n", "3\n", "4\n", "5\n", "6\n", "7\n", "8\n", "9\n", "10\n", "11\n", "12\n", "13\n", "14\n", "15\n", "16\n", "17\n", "18\n", "19\n", "20\n"]}], "source": ["for x in arr3:\n", "    for y in x:\n", "        for z in y:\n", "            print(z)"]}, {"cell_type": "code", "execution_count": 35, "id": "1b85a1a6-7950-4b99-b002-063a10fa9059", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1\n", "2\n", "3\n", "4\n", "5\n"]}], "source": ["for x in np.nditer(arr1):\n", "    print(x)"]}, {"cell_type": "code", "execution_count": 36, "id": "49430d93-b7ec-473f-b983-125e3ec4d5e2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1\n", "2\n", "3\n", "4\n", "5\n", "6\n", "7\n", "8\n", "9\n", "10\n"]}], "source": ["for x in np.nditer(arr2):\n", "    print(x)"]}, {"cell_type": "code", "execution_count": 37, "id": "84d9eb4a-45d8-480f-8070-1e98cd9bf432", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1\n", "2\n", "3\n", "4\n", "5\n", "6\n", "7\n", "8\n", "9\n", "10\n", "11\n", "12\n", "13\n", "14\n", "15\n", "16\n", "17\n", "18\n", "19\n", "20\n"]}], "source": ["for x in np.nditer(arr3):\n", "    print(x)"]}, {"cell_type": "code", "execution_count": 38, "id": "6005d7a5-88f1-41cf-9dac-76e1617fd253", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[3 4]\n", " [8 9]]\n"]}], "source": ["print(arr2[0:2 , 2:4 ])"]}, {"cell_type": "code", "execution_count": 39, "id": "a355cc90-d618-4c8f-bf0f-55a0acde7bc2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["3\n", "4\n", "8\n", "9\n"]}], "source": ["for x in np.nditer(arr2[0:2 , 2:4 ]):\n", "    print(x)"]}, {"cell_type": "code", "execution_count": 40, "id": "adeb8f62-8530-49d1-8857-460555eb6d5c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(0, 0) 1\n", "(0, 1) 2\n", "(0, 2) 3\n", "(0, 3) 4\n", "(0, 4) 5\n", "(1, 0) 6\n", "(1, 1) 7\n", "(1, 2) 8\n", "(1, 3) 9\n", "(1, 4) 10\n"]}], "source": ["for index, x in np.ndenumerate(arr2):\n", "    print(index, x)"]}, {"cell_type": "code", "execution_count": 41, "id": "e4ed15e5-ffcb-4fbb-b46c-99b02d186fc9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(0, 0) 3\n", "(0, 1) 4\n", "(1, 0) 8\n", "(1, 1) 9\n"]}], "source": ["for index, x in np.ndenumerate(arr2[0:2 , 2:4 ]):\n", "    print(index, x)"]}, {"cell_type": "code", "execution_count": 42, "id": "50fc8e67-f056-4587-aa80-438eaee4be39", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[ 1  2  3  4  5]\n", " [ 6  7  8  9 10]]\n"]}], "source": ["print(arr2)"]}, {"cell_type": "code", "execution_count": 43, "id": "4e56a264-0491-484f-928b-cc5f33603938", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[1 2 3 4 5 6]\n"]}], "source": ["ar1 = np.array([1,2,3])\n", "ar2 = np.array([4,5,6])\n", "ar = np.concatenate((ar1 , ar2))\n", "print(ar)"]}, {"cell_type": "code", "execution_count": 44, "id": "7c949e96-f8c2-4fa7-a704-ab2c2ae8b05f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[1 2 5 6]\n", " [3 4 7 8]]\n", "--------------\n", "[[1 2]\n", " [3 4]\n", " [5 6]\n", " [7 8]]\n"]}], "source": ["ar21 = np.array([[1,2],[3,4]])\n", "ar22 = np.array([[5,6],[7,8]])\n", "ar1 = np.concatenate((ar21 , ar22), axis=1)\n", "ar0 = np.concatenate((ar21 , ar22), axis=0)\n", "print(ar1)\n", "print(\"--------------\")\n", "print(ar0)"]}, {"cell_type": "code", "execution_count": 45, "id": "835b3318-10c4-48bc-891f-702008a8b44a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[[1 2]\n", "  [5 6]]\n", "\n", " [[3 4]\n", "  [7 8]]]\n"]}], "source": ["arrstack = np.stack((ar21 , ar22), axis=1)\n", "print(arrstack)"]}, {"cell_type": "code", "execution_count": 46, "id": "d69f05db-61ab-4611-b80c-1beec01cc272", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[1 2 5 6]\n", " [3 4 7 8]]\n"]}], "source": ["arrhstack = np.hstack((ar21 , ar22))\n", "print(arrhstack)"]}, {"cell_type": "code", "execution_count": 47, "id": "6597dc41-1752-41ae-b13b-b8d88ca1e939", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[1 2]\n", " [3 4]\n", " [5 6]\n", " [7 8]]\n"]}], "source": ["arrvstack = np.vstack((ar21 , ar22))\n", "print(arrvstack)"]}, {"cell_type": "code", "execution_count": 48, "id": "f02c11fe-7084-426b-81fd-ff5d88f54711", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[[1 5]\n", "  [2 6]]\n", "\n", " [[3 7]\n", "  [4 8]]]\n"]}], "source": ["arrdstack = np.dstack((ar21 , ar22))\n", "print(arrdstack)"]}, {"cell_type": "code", "execution_count": 49, "id": "c699e759-bd7b-4b79-8c10-37b427435c8c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[[1 2]\n", "  [3 4]]\n", "\n", " [[5 6]\n", "  [7 8]]]\n"]}], "source": ["arrstack = np.stack((ar21 , ar22), axis=0)\n", "print(arrstack)"]}, {"cell_type": "code", "execution_count": 50, "id": "21f33a70-1e53-455f-b8a7-ea154aa7c9a8", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([ 2,  5,  8,  9,  6,  4, 58, 65, 45, 58, 95, 68])"]}, "execution_count": 50, "metadata": {}, "output_type": "execute_result"}], "source": ["arr = np.array([2,5,8,9,6,4,58,65,45,58,95,68])\n", "arr"]}, {"cell_type": "code", "execution_count": 51, "id": "56de3b56-52d7-4d00-bcd5-184493c68ee0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[ 2  5  8  9  6  4 58 65 45 58 95 68]\n"]}], "source": ["print(arr)"]}, {"cell_type": "code", "execution_count": 52, "id": "2c224d8e-ead0-4055-a2da-9822ddedfe15", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[array([2, 5, 8]), array([9, 6, 4]), array([58, 65]), array([45, 58]), array([95, 68])]\n"]}], "source": ["split_array = np.array_split(arr , 5)\n", "print(split_array)"]}, {"cell_type": "code", "execution_count": 53, "id": "946ad212-bd3d-4e8d-98aa-d261d4b1846c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[array([2, 5]), array([8, 9]), array([6]), array([4]), array([58]), array([65]), array([45]), array([58]), array([95]), array([68])]\n"]}], "source": ["split_array = np.array_split(arr , 10)\n", "print(split_array)"]}, {"cell_type": "code", "execution_count": 54, "id": "ceb74108-3b28-4799-b253-57a37f455edd", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[array([2]), array([5]), array([8]), array([9]), array([6]), array([4]), array([58]), array([65]), array([45]), array([58]), array([95]), array([68]), array([], dtype=int64), array([], dtype=int64), array([], dtype=int64)]\n"]}], "source": ["split_array = np.array_split(arr , 15)\n", "print(split_array)"]}, {"cell_type": "code", "execution_count": 56, "id": "fc729857-b56e-4037-a222-ce7d7fb72819", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[ 2  5]\n", " [ 8  9]\n", " [ 6  4]\n", " [58 65]\n", " [45 58]\n", " [95 68]]\n"]}], "source": ["arr2 = np.array([[ 2,  5],  [8,  9],  [6,  4], [58, 65], [45, 58], [95, 68 ]])\n", "print(arr2)"]}, {"cell_type": "code", "execution_count": 57, "id": "ebbaeb22-6468-45dd-91f0-596f5074d320", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[array([[2, 5],\n", "       [8, 9]]), array([[ 6,  4],\n", "       [58, 65]]), array([[45, 58],\n", "       [95, 68]])]\n"]}], "source": ["split_array = np.array_split(arr2 , 3)\n", "print(split_array)"]}, {"cell_type": "code", "execution_count": 58, "id": "6f6b9e2b-6dac-4ffe-9588-2edd66e41747", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[array([[2, 5],\n", "       [8, 9]]), array([[ 6,  4],\n", "       [58, 65]]), array([[45, 58]]), array([[95, 68]])]\n"]}], "source": ["split_array = np.array_split(arr2 , 4)\n", "print(split_array)"]}, {"cell_type": "code", "execution_count": 67, "id": "9f369cab-72c9-4c1d-b14a-074bbb568413", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2\n"]}], "source": ["print(split_array[0][0,0])"]}, {"cell_type": "code", "execution_count": 59, "id": "9b91b0ab-558e-4e81-9d42-0e585e2ac66f", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([ 2,  5,  8,  9,  6,  4, 58, 65, 45, 58, 95, 68])"]}, "execution_count": 59, "metadata": {}, "output_type": "execute_result"}], "source": ["arr = np.array([2,5,8,9,6,4,58,65,45,58,95,68])\n", "arr"]}, {"cell_type": "code", "execution_count": 62, "id": "8e8a7070-64db-4470-beb2-e7d4e58f0ec8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(array([6, 9]),)\n"]}], "source": ["z = np.where(arr == 58)\n", "print(z)"]}, {"cell_type": "code", "execution_count": 61, "id": "6629031e-bcbd-4198-9c24-25991ce37cdd", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(array([ 0,  2,  4,  5,  6,  9, 11]),)\n"]}], "source": ["even = np.where(arr%2 == 0 )\n", "print(even)"]}, {"cell_type": "code", "execution_count": 68, "id": "dd3cd5e8-b86b-4007-9c84-d785b5066e3c", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([ 2,  8,  6,  4, 58, 58, 68])"]}, "execution_count": 68, "metadata": {}, "output_type": "execute_result"}], "source": ["arr[even]"]}, {"cell_type": "code", "execution_count": 63, "id": "cd29cf31-8ef0-483d-90fe-2090bc3ac4f7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(array([], dtype=int64),)\n"]}], "source": ["z = np.where(arr == 158)\n", "print(z)"]}, {"cell_type": "code", "execution_count": 70, "id": "f814d53f-01c8-44e0-b6e9-bef2cebb2cd1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["8\n", "58\n", "58\n", "68\n", "85\n"]}], "source": ["arr = np.array([2,5,8,9,6,4,58,65,45,58,95,68, 85])\n", "\n", "for x in arr:\n", "    if '8' in str(x):\n", "        print(x)"]}, {"cell_type": "code", "execution_count": 71, "id": "1df92b08-4b64-42d6-b7eb-fb8c93f7b8e6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[ 2  4  5  6  8  9 45 58 58 65 68 85 95]\n"]}], "source": ["print(np.sort(arr))"]}, {"cell_type": "code", "execution_count": null, "id": "837cbebb-4d9e-4fd4-b9bf-957e2008f9c2", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.7"}}, "nbformat": 4, "nbformat_minor": 5}