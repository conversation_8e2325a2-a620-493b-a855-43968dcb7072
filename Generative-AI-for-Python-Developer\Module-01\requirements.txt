#	Python 3.11+
#	Anaconda/Miniconda
#	VS Code or Jupyter Notebook
#   API key for OpenAI & Hugging Face

# python -m venv genai
# .\genai\Scripts\Activate.ps1
# pip install -r requirements.txt

# ===== Core Python Packages =====
numpy>=1.24.0
pandas>=2.0.0
matplotlib>=3.7.0
scikit-learn>=1.7.2
jupyterlab>=4.0.0

# ===== Deep Learning Frameworks =====
torch>=2.0.0
torchvision>=0.15.0
torchaudio>=2.8.0
tensorflow>=2.13.0

# ===== Hugging Face & Transformers =====
transformers>=4.34.0
datasets>=2.15.0
accelerate>=0.23.0
evaluate>=0.4.0

# ===== Generative AI APIs =====
openai>=1.3.0
python-dotenv>=1.1.1  # for managing API keys securely

# ===== Image Generation =====
diffusers>=0.21.0
safetensors>=0.4.0
Pillow>=10.0.0

# ===== RAG, <PERSON><PERSON><PERSON><PERSON> & Vector DBs =====
langchain>=0.0.330
llama-index>=0.9.15
chromadb>=0.4.13
pinecone-client>=2.2.4
sentence-transformers>=2.2.2

# ===== Web & App Development =====
flask>=2.3.0
streamlit>=1.27.0
fastapi>=0.103.0
uvicorn[standard]>=0.23.0

# ===== Agentic AI & Automation =====
autogen>=0.2.0
crewai>=0.1.10
langgraph>=0.0.20

# ===== Monitoring & Logging =====
prometheus-client>=0.17.1

# ===== Utility Packages =====
requests>=2.31.0
python-multipart>=0.0.6  # for FastAPI file uploads
