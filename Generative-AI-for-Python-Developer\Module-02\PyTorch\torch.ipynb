{"cells": [{"cell_type": "code", "execution_count": 1, "id": "789fc4a5-93fa-467c-ace8-d11215bd183d", "metadata": {}, "outputs": [{"data": {"text/plain": ["'2.8.0+cpu'"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import torch\n", "torch.__version__"]}, {"cell_type": "code", "execution_count": 2, "id": "f8d945e1-7f94-4b9a-a433-031c373e4711", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor(7)"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["# Scalar\n", "scalar = torch.tensor(7)\n", "scalar"]}, {"cell_type": "code", "execution_count": 3, "id": "bc99337e-9a29-4fb3-955d-321c09c5a920", "metadata": {}, "outputs": [{"data": {"text/plain": ["0"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["scalar.ndim"]}, {"cell_type": "code", "execution_count": 4, "id": "e83ebeb3-792e-4d45-9dea-c1f9666161a0", "metadata": {}, "outputs": [{"data": {"text/plain": ["7"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["# Get the Python number within a tensor (only works with one-element tensors)\n", "scalar.item()"]}, {"cell_type": "code", "execution_count": 5, "id": "8cce4c15-3b4d-424c-aed5-e2d29ba14f21", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([7, 7])"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["# Vector\n", "vector = torch.tensor([7, 7])\n", "vector"]}, {"cell_type": "code", "execution_count": 6, "id": "c1997d86-c9fa-4a62-868b-dfa0ea51647c", "metadata": {}, "outputs": [{"data": {"text/plain": ["1"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["# Check the number of dimensions of vector\n", "vector.ndim"]}, {"cell_type": "code", "execution_count": 7, "id": "a50c6088-e7ff-4836-b55e-c8fc3bf82bda", "metadata": {}, "outputs": [{"data": {"text/plain": ["torch.Size([2])"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["# Check shape of vector\n", "vector.shape"]}, {"cell_type": "code", "execution_count": 8, "id": "b7ce6933-afcf-4d5c-bc84-26c08fcc1dbb", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[ 7,  8],\n", "        [ 9, 10]])"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["# Matrix\n", "Matrix = torch.tensor([[7, 8], \n", "                       [9, 10]])\n", "Matrix"]}, {"cell_type": "code", "execution_count": 9, "id": "0fcf4655-d1dd-4c74-b1f0-3dede2d866e7", "metadata": {}, "outputs": [{"data": {"text/plain": ["2"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["# Check number of dimensions\n", "Matrix.ndim"]}, {"cell_type": "code", "execution_count": 10, "id": "90807e95-14d0-4297-a9ed-3dffe5837f72", "metadata": {}, "outputs": [{"data": {"text/plain": ["torch.<PERSON><PERSON>([2, 2])"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["Matrix.shape"]}, {"cell_type": "code", "execution_count": 11, "id": "b6564468-bf2d-449b-86a2-e165e10fa0ad", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[[1, 2, 3],\n", "         [3, 6, 9],\n", "         [2, 4, 5]]])"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["# Tensor\n", "Tensor = torch.tensor([[[1, 2, 3],\n", "                        [3, 6, 9],\n", "                        [2, 4, 5]]])\n", "Tensor"]}, {"cell_type": "code", "execution_count": 12, "id": "de143b7d-101c-49f5-8474-7b57bf715047", "metadata": {}, "outputs": [{"data": {"text/plain": ["3"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["# Check number of dimensions for TENSOR\n", "Tensor.ndim"]}, {"cell_type": "code", "execution_count": 28, "id": "4ca2b212-ac5f-4c87-8714-2de2bc3d172c", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[1, 2, 3],\n", "        [3, 6, 9],\n", "        [2, 4, 5]])"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["Tensor[0]"]}, {"cell_type": "code", "execution_count": 13, "id": "8d6381e8-3b4e-466c-854e-8863c4869289", "metadata": {}, "outputs": [{"data": {"text/plain": ["torch.Si<PERSON>([1, 3, 3])"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["Tensor.shape"]}, {"cell_type": "markdown", "id": "532e24a3-68d3-49d1-8b6d-bed87238a7b4", "metadata": {}, "source": ["Name\tWhat is it?\t\n", "scalar\ta single number\t\n", "vector\ta number with direction (e.g. wind speed with direction) but can also have many other numbers\t\n", "matrix\ta 2-dimensional array of numbers\t\n", "tensor\tan n-dimensional array of numbers"]}, {"cell_type": "markdown", "id": "29aff6be-3f6d-4e0d-b3a6-185f5da92a59", "metadata": {}, "source": ["# Random tensors"]}, {"cell_type": "code", "execution_count": 15, "id": "22166972-7eae-4325-9c5f-6579e9863b0c", "metadata": {}, "outputs": [{"data": {"text/plain": ["(tensor([[0.6025, 0.3613, 0.9996, 0.0672],\n", "         [0.5727, 0.1430, 0.0066, 0.8757],\n", "         [0.3771, 0.6192, 0.1877, 0.4850]]),\n", " torch.float32)"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["# Create a random tensor of size (3, 4)\n", "random_tensor = torch.rand(size=(3, 4))\n", "random_tensor, random_tensor.dtype"]}, {"cell_type": "code", "execution_count": 16, "id": "d3d6de9e-7eb7-488b-baac-59d2e9c11721", "metadata": {}, "outputs": [{"data": {"text/plain": ["(<PERSON><PERSON><PERSON><PERSON>([224, 224, 3]), 3)"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["# Create a random tensor of size (224, 224, 3)\n", "random_image_size_tensor = torch.rand(size=(224, 224, 3))\n", "random_image_size_tensor.shape, random_image_size_tensor.ndim"]}, {"cell_type": "code", "execution_count": 17, "id": "dcae3403-6fb1-4194-b48e-ee05ce2ecedf", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[[0.7726, 0.7662, 0.1519],\n", "         [0.5839, 0.2689, 0.9026],\n", "         [0.4445, 0.9891, 0.6635],\n", "         ...,\n", "         [0.8572, 0.7307, 0.3117],\n", "         [0.7460, 0.1882, 0.6167],\n", "         [0.6227, 0.4568, 0.5771]],\n", "\n", "        [[0.9133, 0.5713, 0.1353],\n", "         [0.0539, 0.5998, 0.4116],\n", "         [0.9903, 0.5691, 0.6384],\n", "         ...,\n", "         [0.0815, 0.7310, 0.0300],\n", "         [0.8532, 0.3411, 0.2226],\n", "         [0.8412, 0.9128, 0.9824]],\n", "\n", "        [[0.9811, 0.0483, 0.7150],\n", "         [0.1925, 0.6339, 0.0970],\n", "         [0.6348, 0.6196, 0.2084],\n", "         ...,\n", "         [0.3107, 0.4126, 0.8037],\n", "         [0.7503, 0.1460, 0.4901],\n", "         [0.5940, 0.5917, 0.2621]],\n", "\n", "        ...,\n", "\n", "        [[0.0677, 0.6913, 0.0379],\n", "         [0.3850, 0.5924, 0.6873],\n", "         [0.4151, 0.9181, 0.1387],\n", "         ...,\n", "         [0.3402, 0.1560, 0.2763],\n", "         [0.9197, 0.1219, 0.9933],\n", "         [0.2852, 0.4331, 0.5021]],\n", "\n", "        [[0.5455, 0.4530, 0.0431],\n", "         [0.6499, 0.1669, 0.9559],\n", "         [0.5672, 0.7007, 0.3033],\n", "         ...,\n", "         [0.3984, 0.2618, 0.1333],\n", "         [0.7741, 0.5241, 0.8814],\n", "         [0.9532, 0.0058, 0.6661]],\n", "\n", "        [[0.1180, 0.7933, 0.2235],\n", "         [0.5625, 0.6519, 0.0708],\n", "         [0.6392, 0.8118, 0.1824],\n", "         ...,\n", "         [0.4957, 0.2043, 0.7754],\n", "         [0.8664, 0.6833, 0.2744],\n", "         [0.3427, 0.4666, 0.1177]]])"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["random_image_size_tensor"]}, {"cell_type": "markdown", "id": "3b4c77d6-aab6-4ca1-8997-8091fd00cc46", "metadata": {}, "source": ["## Zeros and ones"]}, {"cell_type": "code", "execution_count": 18, "id": "c6b7d3cb-2034-42e2-871a-4ceb48d61e71", "metadata": {}, "outputs": [{"data": {"text/plain": ["(tensor([[0., 0., 0., 0.],\n", "         [0., 0., 0., 0.],\n", "         [0., 0., 0., 0.]]),\n", " torch.float32)"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["# Create a tensor of all zeros\n", "zeros = torch.zeros(size=(3, 4))\n", "zeros, zeros.dtype"]}, {"cell_type": "code", "execution_count": 19, "id": "c6540707-8b56-4296-bd5c-ca1e3102cdd8", "metadata": {}, "outputs": [{"data": {"text/plain": ["(tensor([[1., 1., 1., 1.],\n", "         [1., 1., 1., 1.],\n", "         [1., 1., 1., 1.]]),\n", " torch.float32)"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["# Create a tensor of all ones\n", "ones = torch.ones(size=(3, 4))\n", "ones, ones.dtype"]}, {"cell_type": "markdown", "id": "961a1c4e-b055-4c64-92c0-5db961bc5c4b", "metadata": {}, "source": ["# Creating a range and tensors"]}, {"cell_type": "code", "execution_count": 21, "id": "ed455820-d023-4e78-9750-d5e2840303eb", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([0, 1, 2, 3, 4, 5, 6, 7, 8, 9])"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["# torch.arange(start, end, step)\n", "\n", "# Create a range of values 0 to 10\n", "zero_to_ten = torch.arange(start=0, end=10, step=1)\n", "zero_to_ten"]}, {"cell_type": "markdown", "id": "c9f85d6a-119e-4d59-afbb-85f74dd596c4", "metadata": {}, "source": ["# Tensor datatypes"]}, {"cell_type": "code", "execution_count": 22, "id": "02572949-0fd8-4a8a-a2a1-4da9f18e9beb", "metadata": {}, "outputs": [{"data": {"text/plain": ["(torch.Size([3]), torch.float32, device(type='cpu'))"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["# Default datatype for tensors is float32\n", "float_32_tensor = torch.tensor([3.0, 6.0, 9.0],\n", "                               dtype=None, # defaults to None, which is torch.float32 or whatever datatype is passed\n", "                               device=None, # defaults to None, which uses the default tensor type\n", "                               requires_grad=False) # if True, operations performed on the tensor are recorded \n", "\n", "float_32_tensor.shape, float_32_tensor.dtype, float_32_tensor.device"]}, {"cell_type": "code", "execution_count": 23, "id": "86c85b14-19de-4046-bc68-d7bc3df82248", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["tensor([[0.3164, 0.0410, 0.1581, 0.8055],\n", "        [0.5404, 0.9276, 0.6445, 0.8938],\n", "        [0.6646, 0.6961, 0.4993, 0.4986]])\n", "Shape of tensor: <PERSON>.<PERSON><PERSON>([3, 4])\n", "Datatype of tensor: torch.float32\n", "Device tensor is stored on: cpu\n"]}], "source": ["# Create a tensor\n", "some_tensor = torch.rand(3, 4)\n", "\n", "# Find out details about it\n", "print(some_tensor)\n", "print(f\"Shape of tensor: {some_tensor.shape}\")\n", "print(f\"Datatype of tensor: {some_tensor.dtype}\")\n", "print(f\"Device tensor is stored on: {some_tensor.device}\") # will default to CPU"]}, {"cell_type": "markdown", "id": "36c45896-ba1d-412c-a424-f5125ee83e7b", "metadata": {}, "source": ["# Basic operations"]}, {"cell_type": "code", "execution_count": 24, "id": "7fe489b3-a139-42db-ba66-4bba52a5aad9", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([11, 12, 13])"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["# Create a tensor of values and add a number to it\n", "tensor = torch.tensor([1, 2, 3])\n", "tensor + 10"]}, {"cell_type": "code", "execution_count": 25, "id": "032026ea-d3d9-4645-8e96-6d9b3d11dc5c", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([10, 20, 30])"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["# Multiply it by 10\n", "tensor * 10"]}, {"cell_type": "code", "execution_count": 26, "id": "379b3af8-272c-4d24-b657-f0f9f3fc7684", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([1, 2, 3])"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["# Tensors don't change unless reassigned\n", "tensor"]}, {"cell_type": "code", "execution_count": 27, "id": "75319d89-fef2-4617-9bc4-6b985c365c3e", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([10, 20, 30])"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["# Can also use torch functions\n", "torch.multiply(tensor, 10)"]}, {"cell_type": "code", "execution_count": 29, "id": "cb390007-8441-473c-a04d-2112097a21d4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["tensor([1, 2, 3]) * tensor([1, 2, 3])\n", "Equals: tensor([1, 4, 9])\n"]}], "source": ["# Element-wise multiplication (each element multiplies its equivalent, index 0->0, 1->1, 2->2)\n", "print(tensor, \"*\", tensor)\n", "print(\"Equals:\", tensor * tensor)"]}, {"cell_type": "markdown", "id": "2743bec4-ef40-48d1-a390-af2c0f52442c", "metadata": {}, "source": ["# Matrix multiplication"]}, {"cell_type": "code", "execution_count": 30, "id": "b90eec7c-ead7-47f3-90c8-c73d9e86f121", "metadata": {}, "outputs": [{"data": {"text/plain": ["torch.<PERSON><PERSON>([3])"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["import torch\n", "tensor = torch.tensor([1, 2, 3])\n", "tensor.shape"]}, {"cell_type": "code", "execution_count": 31, "id": "d9282ab5-8f4b-4702-a6d4-d8a7fd638614", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([1, 2, 3])"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["tensor"]}, {"cell_type": "code", "execution_count": 32, "id": "45fe0e08-8794-4d07-a713-a76fca31d33d", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([1, 4, 9])"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["# Element-wise matrix multiplication\n", "tensor * tensor"]}, {"cell_type": "code", "execution_count": 33, "id": "955c1c50-8935-4253-9ea3-ddfea968ce29", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor(14)"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["# Matrix multiplication\n", "torch.matmul(tensor, tensor)"]}, {"cell_type": "code", "execution_count": 34, "id": "c2f4b17f-fe78-4068-98cb-968ba0a3ebcf", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CPU times: total: 0 ns\n", "Wall time: 1.62 ms\n"]}, {"data": {"text/plain": ["tensor(14)"]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["%%time\n", "# Matrix multiplication by hand \n", "# (avoid doing operations with for loops at all cost, they are computationally expensive)\n", "value = 0\n", "for i in range(len(tensor)):\n", "  value += tensor[i] * tensor[i]\n", "value"]}, {"cell_type": "code", "execution_count": 35, "id": "cfac627d-d90a-456f-bf82-28d8c7a61aaf", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CPU times: total: 0 ns\n", "Wall time: 75.8 μs\n"]}, {"data": {"text/plain": ["tensor(14)"]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["%%time\n", "torch.matmul(tensor, tensor)"]}, {"cell_type": "markdown", "id": "7e3c0f65-1800-4a5f-801f-d9002c2dc02e", "metadata": {}, "source": ["## Neural networks are full of matrix multiplications and dot products."]}, {"cell_type": "code", "execution_count": 36, "id": "9dde20a0-f022-489a-a615-48be50d22964", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Minimum: 0\n", "Maximum: 90\n", "Mean: 45.0\n", "Sum: 450\n"]}], "source": ["# Create a tensor\n", "x = torch.arange(0, 100, 10)\n", "print(f\"Minimum: {x.min()}\")\n", "print(f\"Maximum: {x.max()}\")\n", "# print(f\"Mean: {x.mean()}\") # this will error\n", "print(f\"Mean: {x.type(torch.float32).mean()}\") # won't work without float datatype\n", "print(f\"Sum: {x.sum()}\")"]}, {"cell_type": "code", "execution_count": 37, "id": "57e4ed29-e4c0-46ec-ba14-437208fca22d", "metadata": {}, "outputs": [{"data": {"text/plain": ["(tensor(90), tensor(0), tensor(45.), tensor(450))"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["torch.max(x), torch.min(x), torch.mean(x.type(torch.float32)), torch.sum(x)"]}, {"cell_type": "code", "execution_count": 38, "id": "eb3b0744-70ee-4a08-ab18-1f1cf411ad1b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Index where max value occurs: 9\n", "Index where min value occurs: 0\n"]}], "source": ["# Returns index of max and min values\n", "print(f\"Index where max value occurs: {x.argmax()}\")\n", "print(f\"Index where min value occurs: {x.argmin()}\")"]}, {"cell_type": "code", "execution_count": 39, "id": "1824ce8a-b3ca-4699-a42d-c3b1f6b19627", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[[1, 2, 3],\n", "         [4, 5, 6],\n", "         [7, 8, 9]]])"]}, "execution_count": 39, "metadata": {}, "output_type": "execute_result"}], "source": ["# Create a tensor \n", "import torch\n", "x = torch.arange(1, 10).reshape(1, 3, 3)\n", "x"]}, {"cell_type": "code", "execution_count": 40, "id": "b9466f76-5136-4dd6-9727-e274f5a3d7b6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["First square bracket:\n", "tensor([[1, 2, 3],\n", "        [4, 5, 6],\n", "        [7, 8, 9]])\n", "Second square bracket: tensor([1, 2, 3])\n", "Third square bracket: 1\n"]}], "source": ["# Let's index bracket by bracket\n", "print(f\"First square bracket:\\n{x[0]}\") \n", "print(f\"Second square bracket: {x[0][0]}\") \n", "print(f\"Third square bracket: {x[0][0][0]}\")"]}, {"cell_type": "code", "execution_count": 42, "id": "686c5b73-5057-47a9-a719-58d7ad635ed7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Tensor A:\n", "tensor([[0.3777, 0.3558, 0.9884, 0.5525],\n", "        [0.1927, 0.8647, 0.6809, 0.6485],\n", "        [0.2678, 0.9570, 0.9014, 0.1172]])\n", "\n", "Tensor B:\n", "tensor([[8.2153e-04, 3.2651e-01, 5.4724e-01, 8.4999e-01],\n", "        [9.6929e-01, 2.5705e-01, 7.8346e-01, 9.8540e-01],\n", "        [8.3985e-01, 7.3218e-01, 2.5031e-01, 3.4933e-01]])\n", "\n", "Does Tensor A equal Tensor B?\n"]}, {"data": {"text/plain": ["tensor([[False, False, False, False],\n", "        [Fals<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>als<PERSON>],\n", "        [False, <PERSON>alse, <PERSON>als<PERSON>, <PERSON>alse]])"]}, "execution_count": 42, "metadata": {}, "output_type": "execute_result"}], "source": ["import torch\n", "\n", "# Create two random tensors\n", "random_tensor_A = torch.rand(3, 4)\n", "random_tensor_B = torch.rand(3, 4)\n", "\n", "print(f\"Tensor A:\\n{random_tensor_A}\\n\")\n", "print(f\"Tensor B:\\n{random_tensor_B}\\n\")\n", "print(f\"Does Tensor A equal Tensor B?\")\n", "random_tensor_A == random_tensor_B"]}, {"cell_type": "code", "execution_count": 43, "id": "5671cfe5-5612-446c-b59b-203dc2ec3a24", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Tensor C:\n", "tensor([[0.8823, 0.9150, 0.3829, 0.9593],\n", "        [0.3904, 0.6009, 0.2566, 0.7936],\n", "        [0.9408, 0.1332, 0.9346, 0.5936]])\n", "\n", "Tensor D:\n", "tensor([[0.8823, 0.9150, 0.3829, 0.9593],\n", "        [0.3904, 0.6009, 0.2566, 0.7936],\n", "        [0.9408, 0.1332, 0.9346, 0.5936]])\n", "\n", "Does Tensor C equal Tensor D? (anywhere)\n"]}, {"data": {"text/plain": ["tensor([[True, True, True, True],\n", "        [True, True, True, True],\n", "        [True, True, True, True]])"]}, "execution_count": 43, "metadata": {}, "output_type": "execute_result"}], "source": ["# # Set the random seed\n", "RANDOM_SEED=42 \n", "torch.manual_seed(seed=RANDOM_SEED) \n", "random_tensor_C = torch.rand(3, 4)\n", "torch.random.manual_seed(seed=RANDOM_SEED) \n", "random_tensor_D = torch.rand(3, 4)\n", "\n", "print(f\"Tensor C:\\n{random_tensor_C}\\n\")\n", "print(f\"Tensor D:\\n{random_tensor_D}\\n\")\n", "print(f\"Does Tensor C equal Tensor D? (anywhere)\")\n", "random_tensor_C == random_tensor_D"]}, {"cell_type": "markdown", "id": "65172800-ab33-4953-be04-fade1da419c5", "metadata": {}, "source": ["# PyTorch Workflow Fundamentals\n", "- data (prepare and load)\n", "- build model\n", "- fitting the model to data (training)\n", "- making predictions and evaluating a model (inference)\n", "- saving and loading a model"]}, {"cell_type": "code", "execution_count": 9, "id": "aa493700-1d84-47d0-8b63-acd23887045a", "metadata": {}, "outputs": [{"data": {"text/plain": ["'2.8.0+cpu'"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["import torch\n", "from torch import nn # nn contains all of PyTorch's building blocks for neural networks\n", "import matplotlib.pyplot as plt\n", "\n", "# Check PyTorch version\n", "torch.__version__"]}, {"cell_type": "code", "execution_count": 10, "id": "edeb9302-47e1-4c80-93b2-c23468a51bf0", "metadata": {}, "outputs": [], "source": ["# Create *known* parameters\n", "weight = 0.7\n", "bias = 0.3"]}, {"cell_type": "code", "execution_count": 11, "id": "2025ac0f-8743-4f43-b0c2-7f95e35197f7", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[0.0000],\n", "        [0.0200],\n", "        [0.0400],\n", "        [0.0600],\n", "        [0.0800],\n", "        [0.1000],\n", "        [0.1200],\n", "        [0.1400],\n", "        [0.1600],\n", "        [0.1800],\n", "        [0.2000],\n", "        [0.2200],\n", "        [0.2400],\n", "        [0.2600],\n", "        [0.2800],\n", "        [0.3000],\n", "        [0.3200],\n", "        [0.3400],\n", "        [0.3600],\n", "        [0.3800],\n", "        [0.4000],\n", "        [0.4200],\n", "        [0.4400],\n", "        [0.4600],\n", "        [0.4800],\n", "        [0.5000],\n", "        [0.5200],\n", "        [0.5400],\n", "        [0.5600],\n", "        [0.5800],\n", "        [0.6000],\n", "        [0.6200],\n", "        [0.6400],\n", "        [0.6600],\n", "        [0.6800],\n", "        [0.7000],\n", "        [0.7200],\n", "        [0.7400],\n", "        [0.7600],\n", "        [0.7800],\n", "        [0.8000],\n", "        [0.8200],\n", "        [0.8400],\n", "        [0.8600],\n", "        [0.8800],\n", "        [0.9000],\n", "        [0.9200],\n", "        [0.9400],\n", "        [0.9600],\n", "        [0.9800]])"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["# Create data\n", "start = 0\n", "end = 1\n", "step = 0.02\n", "X = torch.arange(start, end, step).unsqueeze(dim=1)\n", "X"]}, {"cell_type": "code", "execution_count": 12, "id": "d04f95ac-3819-42d3-bd3a-9cb143b7e49c", "metadata": {}, "outputs": [{"data": {"text/plain": ["(tensor([[0.0000],\n", "         [0.0200],\n", "         [0.0400],\n", "         [0.0600],\n", "         [0.0800],\n", "         [0.1000],\n", "         [0.1200],\n", "         [0.1400],\n", "         [0.1600],\n", "         [0.1800]]),\n", " tensor([[0.3000],\n", "         [0.3140],\n", "         [0.3280],\n", "         [0.3420],\n", "         [0.3560],\n", "         [0.3700],\n", "         [0.3840],\n", "         [0.3980],\n", "         [0.4120],\n", "         [0.4260]]))"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["y = weight * X + bias\n", "\n", "X[:10], y[:10]"]}, {"cell_type": "code", "execution_count": 13, "id": "623532af-b746-4d4a-a8a0-2867e9bb5eec", "metadata": {}, "outputs": [{"data": {"text/plain": ["(40, 40, 10, 10)"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["# Split data into training and test sets\n", "# Create train/test split\n", "train_split = int(0.8 * len(X)) # 80% of data used for training set, 20% for testing \n", "X_train, y_train = X[:train_split], y[:train_split]\n", "X_test, y_test = X[train_split:], y[train_split:]\n", "\n", "len(X_train), len(y_train), len(X_test), len(y_test)"]}, {"cell_type": "code", "execution_count": 14, "id": "1809be67-9187-4752-9d93-d6ddc650a4c7", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x700 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["def plot_predictions(train_data=X_train, \n", "                     train_labels=y_train, \n", "                     test_data=X_test, \n", "                     test_labels=y_test, \n", "                     predictions=None):\n", "  \"\"\"\n", "  Plots training data, test data and compares predictions.\n", "  \"\"\"\n", "  plt.figure(figsize=(10, 7))\n", "\n", "  # Plot training data in blue\n", "  plt.scatter(train_data, train_labels, c=\"b\", s=4, label=\"Training data\")\n", "  \n", "  # Plot test data in green\n", "  plt.scatter(test_data, test_labels, c=\"g\", s=4, label=\"Testing data\")\n", "\n", "  if predictions is not None:\n", "    # Plot the predictions in red (predictions were made on the test data)\n", "    plt.scatter(test_data, predictions, c=\"r\", s=4, label=\"Predictions\")\n", "\n", "  # Show the legend\n", "  plt.legend(prop={\"size\": 14});\n", "\n", "plot_predictions();"]}, {"cell_type": "markdown", "id": "64be2c49-34c7-4ff9-9ee9-523f5844a648", "metadata": {}, "source": ["# Build model \n", "- LinearRegressionModel"]}, {"cell_type": "code", "execution_count": 15, "id": "002378b5-7282-4448-a051-f4d31beccedc", "metadata": {}, "outputs": [], "source": ["# Create a Linear Regression model class\n", "class LinearRegressionModel(nn.<PERSON><PERSON><PERSON>): # <- almost everything in PyTorch is a nn.Module (think of this as neural network lego blocks)\n", "    def __init__(self):\n", "        super().__init__() \n", "        self.weights = nn.Parameter(torch.randn(1, # <- start with random weights (this will get adjusted as the model learns)\n", "                                                dtype=torch.float), # <- PyTorch loves float32 by default\n", "                                   requires_grad=True) # <- can we update this value with gradient descent?)\n", "\n", "        self.bias = nn.Parameter(torch.randn(1, # <- start with random bias (this will get adjusted as the model learns)\n", "                                            dtype=torch.float), # <- PyTorch loves float32 by default\n", "                                requires_grad=True) # <- can we update this value with gradient descent?))\n", "\n", "    # Forward defines the computation in the model\n", "    def forward(self, x: torch.Tensor) -> torch.Tensor: # <- \"x\" is the input data (e.g. training/testing features)\n", "        return self.weights * x + self.bias # <- this is the linear regression formula (y = m*x + b)"]}, {"cell_type": "code", "execution_count": 16, "id": "fb8b5958-cd74-4585-9637-aea92df65493", "metadata": {}, "outputs": [{"data": {"text/plain": ["[Parameter containing:\n", " tensor([0.3367], requires_grad=True),\n", " Parameter containing:\n", " tensor([0.1288], requires_grad=True)]"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["# Set manual seed since nn.Parameter are randomly initialized\n", "torch.manual_seed(42)\n", "\n", "# Create an instance of the model (this is a subclass of nn.Module that contains nn.Parameter(s))\n", "model_0 = LinearRegressionModel()\n", "\n", "# Check the nn.Parameter(s) within the nn.Module subclass we created\n", "list(model_0.parameters())"]}, {"cell_type": "code", "execution_count": 17, "id": "d698959f-9310-4e82-af13-4eeb91197452", "metadata": {}, "outputs": [{"data": {"text/plain": ["OrderedDict([('weights', tensor([0.3367])), ('bias', tensor([0.1288]))])"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["# List named parameters \n", "model_0.state_dict()"]}, {"cell_type": "code", "execution_count": 18, "id": "64f0f087-50c1-492a-a293-b5e4b7222df9", "metadata": {}, "outputs": [], "source": ["# Make predictions with model\n", "with torch.inference_mode(): \n", "    y_preds = model_0(X_test)"]}, {"cell_type": "code", "execution_count": 19, "id": "7842abaf-e8f3-4286-84d3-009ae4e1b726", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Number of testing samples: 10\n", "Number of predictions made: 10\n", "Predicted values:\n", "tensor([[0.3982],\n", "        [0.4049],\n", "        [0.4116],\n", "        [0.4184],\n", "        [0.4251],\n", "        [0.4318],\n", "        [0.4386],\n", "        [0.4453],\n", "        [0.4520],\n", "        [0.4588]])\n"]}], "source": ["# Check the predictions\n", "print(f\"Number of testing samples: {len(X_test)}\") \n", "print(f\"Number of predictions made: {len(y_preds)}\")\n", "print(f\"Predicted values:\\n{y_preds}\")"]}, {"cell_type": "code", "execution_count": 20, "id": "d7b23845-faf0-487f-953f-2b7dbf4423fe", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x700 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plot_predictions(predictions=y_preds)"]}, {"cell_type": "code", "execution_count": 21, "id": "f1f70c6b-9dc5-4e4b-9c97-a95195a47210", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[0.4618],\n", "        [0.4691],\n", "        [0.4764],\n", "        [0.4836],\n", "        [0.4909],\n", "        [0.4982],\n", "        [0.5054],\n", "        [0.5127],\n", "        [0.5200],\n", "        [0.5272]])"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["y_test - y_preds"]}, {"cell_type": "markdown", "id": "90d35fcc-8942-4aea-a851-bfefb5e17611", "metadata": {}, "source": ["# PyTorch testing loop"]}, {"cell_type": "code", "execution_count": 23, "id": "0f0ee5a6-7365-4494-bfa7-f0aa0f59f567", "metadata": {}, "outputs": [], "source": ["# Create the loss function\n", "loss_fn = nn.L1Loss() # MAE loss is same as L1Loss\n", "\n", "# Create the optimizer\n", "optimizer = torch.optim.SGD(params=model_0.parameters(), # parameters of target model to optimize\n", "                            lr=0.01) # learning rate (how much the optimizer should change parameters at each step, higher=more (less stable), lower=less (might take a long time))"]}, {"cell_type": "code", "execution_count": 25, "id": "e7f85a39-134e-4ceb-b03d-32b1b122ff83", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Epoch: 0 | MAE Train Loss: 0.024458957836031914 | MAE Test Loss: 0.05646304413676262 \n", "Epoch: 2 | MAE Train Loss: 0.02376994863152504 | MAE Test Loss: 0.05494590476155281 \n", "Epoch: 4 | MAE Train Loss: 0.023080935701727867 | MAE Test Loss: 0.0534287691116333 \n", "Epoch: 6 | MAE Train Loss: 0.022391926497220993 | MAE Test Loss: 0.05191164091229439 \n", "Epoch: 8 | MAE Train Loss: 0.02170540764927864 | MAE Test Loss: 0.049707621335983276 \n", "Epoch: 10 | MAE Train Loss: 0.021020207554101944 | MAE Test Loss: 0.04819049686193466 \n", "Epoch: 12 | MAE Train Loss: 0.02033500373363495 | MAE Test Loss: 0.046673357486724854 \n", "Epoch: 14 | MAE Train Loss: 0.019649803638458252 | MAE Test Loss: 0.045156221836805344 \n", "Epoch: 16 | MAE Train Loss: 0.018963487818837166 | MAE Test Loss: 0.04363910108804703 \n", "Epoch: 18 | MAE Train Loss: 0.018274478614330292 | MAE Test Loss: 0.042121969163417816 \n", "Epoch: 20 | MAE Train Loss: 0.01758546568453312 | MAE Test Loss: 0.04060482233762741 \n", "Epoch: 22 | MAE Train Loss: 0.016896454617381096 | MAE Test Loss: 0.0390876941382885 \n", "Epoch: 24 | MAE Train Loss: 0.016210997477173805 | MAE Test Loss: 0.03688368946313858 \n", "Epoch: 26 | MAE Train Loss: 0.015525798313319683 | MAE Test Loss: 0.035366542637348175 \n", "Epoch: 28 | MAE Train Loss: 0.014840595424175262 | MAE Test Loss: 0.03384942561388016 \n", "Epoch: 30 | MAE Train Loss: 0.014155393466353416 | MAE Test Loss: 0.03233227878808975 \n", "Epoch: 32 | MAE Train Loss: 0.013468016870319843 | MAE Test Loss: 0.030815154314041138 \n", "Epoch: 34 | MAE Train Loss: 0.01277900766581297 | MAE Test Loss: 0.02929801307618618 \n", "Epoch: 36 | MAE Train Loss: 0.01208999752998352 | MAE Test Loss: 0.027780896052718163 \n", "Epoch: 38 | MAE Train Loss: 0.011401787400245667 | MAE Test Loss: 0.025576870888471603 \n", "Epoch: 40 | MAE Train Loss: 0.010716589167714119 | MAE Test Loss: 0.024059748277068138 \n", "Epoch: 42 | MAE Train Loss: 0.010031387209892273 | MAE Test Loss: 0.022542614489793777 \n", "Epoch: 44 | MAE Train Loss: 0.009346187114715576 | MAE Test Loss: 0.021025484427809715 \n", "Epoch: 46 | MAE Train Loss: 0.008660981431603432 | MAE Test Loss: 0.019508343189954758 \n", "Epoch: 48 | MAE Train Loss: 0.007972544990479946 | MAE Test Loss: 0.017991220578551292 \n", "Epoch: 50 | MAE Train Loss: 0.0072835334576666355 | MAE Test Loss: 0.016474086791276932 \n", "Epoch: 52 | MAE Train Loss: 0.006594526115804911 | MAE Test Loss: 0.01495695672929287 \n", "Epoch: 54 | MAE Train Loss: 0.005907376762479544 | MAE Test Loss: 0.012752944603562355 \n", "Epoch: 56 | MAE Train Loss: 0.005222178064286709 | MAE Test Loss: 0.011235815472900867 \n", "Epoch: 58 | MAE Train Loss: 0.004536976106464863 | MAE Test Loss: 0.009718680754303932 \n", "Epoch: 60 | MAE Train Loss: 0.0038517764769494534 | MAE Test Loss: 0.008201557211577892 \n", "Epoch: 62 | MAE Train Loss: 0.0031660839449614286 | MAE Test Loss: 0.006684416439384222 \n", "Epoch: 64 | MAE Train Loss: 0.0024770735763013363 | MAE Test Loss: 0.005167287774384022 \n", "Epoch: 66 | MAE Train Loss: 0.0017880648374557495 | MAE Test Loss: 0.003650152590125799 \n", "Epoch: 68 | MAE Train Loss: 0.0011887758737429976 | MAE Test Loss: 0.005023092031478882 \n", "Epoch: 70 | MAE Train Loss: 0.008932482451200485 | MAE Test Loss: 0.005023092031478882 \n", "Epoch: 72 | MAE Train Loss: 0.008932482451200485 | MAE Test Loss: 0.005023092031478882 \n", "Epoch: 74 | MAE Train Loss: 0.008932482451200485 | MAE Test Loss: 0.005023092031478882 \n", "Epoch: 76 | MAE Train Loss: 0.008932482451200485 | MAE Test Loss: 0.005023092031478882 \n", "Epoch: 78 | MAE Train Loss: 0.008932482451200485 | MAE Test Loss: 0.005023092031478882 \n", "Epoch: 80 | MAE Train Loss: 0.008932482451200485 | MAE Test Loss: 0.005023092031478882 \n", "Epoch: 82 | MAE Train Loss: 0.008932482451200485 | MAE Test Loss: 0.005023092031478882 \n", "Epoch: 84 | MAE Train Loss: 0.008932482451200485 | MAE Test Loss: 0.005023092031478882 \n", "Epoch: 86 | MAE Train Loss: 0.008932482451200485 | MAE Test Loss: 0.005023092031478882 \n", "Epoch: 88 | MAE Train Loss: 0.008932482451200485 | MAE Test Loss: 0.005023092031478882 \n", "Epoch: 90 | MAE Train Loss: 0.008932482451200485 | MAE Test Loss: 0.005023092031478882 \n", "Epoch: 92 | MAE Train Loss: 0.008932482451200485 | MAE Test Loss: 0.005023092031478882 \n", "Epoch: 94 | MAE Train Loss: 0.008932482451200485 | MAE Test Loss: 0.005023092031478882 \n", "Epoch: 96 | MAE Train Loss: 0.008932482451200485 | MAE Test Loss: 0.005023092031478882 \n", "Epoch: 98 | MAE Train Loss: 0.008932482451200485 | MAE Test Loss: 0.005023092031478882 \n"]}], "source": ["torch.manual_seed(42)\n", "\n", "# Set the number of epochs (how many times the model will pass over the training data)\n", "epochs = 100\n", "\n", "# Create empty loss lists to track values\n", "train_loss_values = []\n", "test_loss_values = []\n", "epoch_count = []\n", "\n", "for epoch in range(epochs):\n", "    ### Training\n", "\n", "    # Put model in training mode (this is the default state of a model)\n", "    model_0.train()\n", "\n", "    # 1. Forward pass on train data using the forward() method inside \n", "    y_pred = model_0(X_train)\n", "    # print(y_pred)\n", "\n", "    # 2. Calculate the loss (how different are our models predictions to the ground truth)\n", "    loss = loss_fn(y_pred, y_train)\n", "\n", "    # 3. Zero grad of the optimizer\n", "    optimizer.zero_grad()\n", "\n", "    # 4. Loss backwards\n", "    loss.backward()\n", "\n", "    # 5. Progress the optimizer\n", "    optimizer.step()\n", "\n", "    ### Testing\n", "\n", "    # Put the model in evaluation mode\n", "    model_0.eval()\n", "\n", "    with torch.inference_mode():\n", "      # 1. Forward pass on test data\n", "      test_pred = model_0(X_test)\n", "\n", "      # 2. Caculate loss on test data\n", "      test_loss = loss_fn(test_pred, y_test.type(torch.float)) # predictions come in torch.float datatype, so comparisons need to be done with tensors of the same type\n", "\n", "      # Print out what's happening\n", "      if epoch % 2 == 0:\n", "            epoch_count.append(epoch)\n", "            train_loss_values.append(loss.detach().numpy())\n", "            test_loss_values.append(test_loss.detach().numpy())\n", "            print(f\"Epoch: {epoch} | MAE Train Loss: {loss} | MAE Test Loss: {test_loss} \")"]}, {"cell_type": "code", "execution_count": 26, "id": "66938ebc-1ed6-45c0-8ad3-271d6a91199c", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Plot the loss curves\n", "plt.plot(epoch_count, train_loss_values, label=\"Train loss\")\n", "plt.plot(epoch_count, test_loss_values, label=\"Test loss\")\n", "plt.title(\"Training and test loss curves\")\n", "plt.ylabel(\"Loss\")\n", "plt.xlabel(\"Epochs\")\n", "plt.legend();"]}, {"cell_type": "code", "execution_count": 59, "id": "e0a50c3d-7d81-4a07-a259-23e552f68819", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The model learned the following values for weights and bias:\n", "OrderedDict({'weights': tensor([0.5784]), 'bias': tensor([0.3513])})\n", "\n", "And the original values for weights and bias are:\n", "weights: 0.7, bias: 0.3\n"]}], "source": ["# Find our model's learned parameters\n", "print(\"The model learned the following values for weights and bias:\")\n", "print(model_0.state_dict())\n", "print(\"\\nAnd the original values for weights and bias are:\")\n", "print(f\"weights: {weight}, bias: {bias}\")"]}, {"cell_type": "markdown", "id": "8eaae3ee-f2ce-44ad-adda-2b70287575a2", "metadata": {}, "source": ["## Making predictions with a trained PyTorch model"]}, {"cell_type": "code", "execution_count": 60, "id": "e3c83df1-d998-4877-a6db-17eeb560754a", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[0.8141],\n", "        [0.8256],\n", "        [0.8372],\n", "        [0.8488],\n", "        [0.8603],\n", "        [0.8719],\n", "        [0.8835],\n", "        [0.8950],\n", "        [0.9066],\n", "        [0.9182]])"]}, "execution_count": 60, "metadata": {}, "output_type": "execute_result"}], "source": ["# 1. Set the model in evaluation mode\n", "model_0.eval()\n", "\n", "# 2. Setup the inference mode context manager\n", "with torch.inference_mode():\n", "  # 3. Make sure the calculations are done with the model and data on the same device\n", "  # in our case, we haven't setup device-agnostic code yet so our data and model are\n", "  # on the CPU by default.\n", "  # model_0.to(device)\n", "  # X_test = X_test.to(device)\n", "  y_preds = model_0(X_test)\n", "y_preds"]}, {"cell_type": "code", "execution_count": 61, "id": "78bc2aec-1867-4692-8613-8843ab5ff1ea", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x700 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plot_predictions(predictions=y_preds)"]}, {"cell_type": "markdown", "id": "cb7e1977-0c6f-4ed9-928a-a89a05d85cc6", "metadata": {}, "source": ["## Saving and loading a PyTorch model"]}, {"cell_type": "code", "execution_count": 62, "id": "a4e8f4cf-60a9-429e-952f-a8aaf731957b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Saving model to: models\\01_pytorch_workflow_model_0.pth\n"]}], "source": ["from pathlib import Path\n", "\n", "# 1. Create models directory \n", "MODEL_PATH = Path(\"models\")\n", "MODEL_PATH.mkdir(parents=True, exist_ok=True)\n", "\n", "# 2. Create model save path \n", "MODEL_NAME = \"01_pytorch_workflow_model_0.pth\"\n", "MODEL_SAVE_PATH = MODEL_PATH / MODEL_NAME\n", "\n", "# 3. Save the model state dict \n", "print(f\"Saving model to: {MODEL_SAVE_PATH}\")\n", "torch.save(obj=model_0.state_dict(), # only saving the state_dict() only saves the models learned parameters\n", "           f=MODEL_SAVE_PATH) "]}, {"cell_type": "code", "execution_count": 64, "id": "ae99bf95-edca-47e8-b78b-5f7053b10239", "metadata": {}, "outputs": [{"data": {"text/plain": ["<All keys matched successfully>"]}, "execution_count": 64, "metadata": {}, "output_type": "execute_result"}], "source": ["# Instantiate a new instance of our model (this will be instantiated with random weights)\n", "loaded_model_0 = LinearRegressionModel()\n", "\n", "# Load the state_dict of our saved model (this will update the new instance of our model with trained weights)\n", "loaded_model_0.load_state_dict(torch.load(f=MODEL_SAVE_PATH))"]}, {"cell_type": "code", "execution_count": 65, "id": "94b13bf5-5a01-4035-b90f-ac17c2a26fae", "metadata": {}, "outputs": [], "source": ["# 1. Put the loaded model into evaluation mode\n", "loaded_model_0.eval()\n", "\n", "# 2. Use the inference mode context manager to make predictions\n", "with torch.inference_mode():\n", "    loaded_model_preds = loaded_model_0(X_test) # perform a forward pass on the test data with the loaded model"]}, {"cell_type": "code", "execution_count": 66, "id": "0a506899-7bd5-40d1-9867-23f3e41e6bad", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[True],\n", "        [True],\n", "        [True],\n", "        [True],\n", "        [True],\n", "        [True],\n", "        [True],\n", "        [True],\n", "        [True],\n", "        [True]])"]}, "execution_count": 66, "metadata": {}, "output_type": "execute_result"}], "source": ["# Compare previous model predictions with loaded model predictions (these should be the same)\n", "y_preds == loaded_model_preds"]}, {"cell_type": "code", "execution_count": null, "id": "d708afa9-9262-4f38-a877-e184115a824f", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "genai", "language": "python", "name": "genai"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.7"}}, "nbformat": 4, "nbformat_minor": 5}