{"cells": [{"cell_type": "code", "execution_count": 2, "id": "19eb4d8c-4619-4ccb-9983-9af06a9d9726", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2.20.0\n"]}], "source": ["# Import required libraries\n", "import tensorflow as tf\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "# check the version (should be 2.x+)\n", "print(tf.__version__) "]}, {"cell_type": "code", "execution_count": 3, "id": "0471cbda", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>age</th>\n", "      <th>sex</th>\n", "      <th>bmi</th>\n", "      <th>children</th>\n", "      <th>smoker</th>\n", "      <th>region</th>\n", "      <th>charges</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>19</td>\n", "      <td>female</td>\n", "      <td>27.900</td>\n", "      <td>0</td>\n", "      <td>yes</td>\n", "      <td>southwest</td>\n", "      <td>16884.92400</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>18</td>\n", "      <td>male</td>\n", "      <td>33.770</td>\n", "      <td>1</td>\n", "      <td>no</td>\n", "      <td>southeast</td>\n", "      <td>1725.55230</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>28</td>\n", "      <td>male</td>\n", "      <td>33.000</td>\n", "      <td>3</td>\n", "      <td>no</td>\n", "      <td>southeast</td>\n", "      <td>4449.46200</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>33</td>\n", "      <td>male</td>\n", "      <td>22.705</td>\n", "      <td>0</td>\n", "      <td>no</td>\n", "      <td>northwest</td>\n", "      <td>21984.47061</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>32</td>\n", "      <td>male</td>\n", "      <td>28.880</td>\n", "      <td>0</td>\n", "      <td>no</td>\n", "      <td>northwest</td>\n", "      <td>3866.85520</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   age     sex     bmi  children smoker     region      charges\n", "0   19  female  27.900         0    yes  southwest  16884.92400\n", "1   18    male  33.770         1     no  southeast   1725.55230\n", "2   28    male  33.000         3     no  southeast   4449.46200\n", "3   33    male  22.705         0     no  northwest  21984.47061\n", "4   32    male  28.880         0     no  northwest   3866.85520"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["# Read in the insurance dataset\n", "insurance = pd.read_csv(\"insurance.csv\")\n", "# Check out the insurance dataset\n", "insurance.head()"]}, {"cell_type": "markdown", "id": "12c764e2", "metadata": {}, "source": ["- We're going to have to turn the non-numerical columns into numbers (because a neural network can't handle non-numerical inputs). \n", "- To do so, we'll use the get_dummies() method in pandas. It converts categorical variables (like the sex, smoker and region columns)\n", "- into numerical variables using one-hot encoding."]}, {"cell_type": "code", "execution_count": 5, "id": "55d35287", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>age</th>\n", "      <th>bmi</th>\n", "      <th>children</th>\n", "      <th>charges</th>\n", "      <th>sex_female</th>\n", "      <th>sex_male</th>\n", "      <th>smoker_no</th>\n", "      <th>smoker_yes</th>\n", "      <th>region_northeast</th>\n", "      <th>region_northwest</th>\n", "      <th>region_southeast</th>\n", "      <th>region_southwest</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>19</td>\n", "      <td>27.900</td>\n", "      <td>0</td>\n", "      <td>16884.92400</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>18</td>\n", "      <td>33.770</td>\n", "      <td>1</td>\n", "      <td>1725.55230</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>28</td>\n", "      <td>33.000</td>\n", "      <td>3</td>\n", "      <td>4449.46200</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>33</td>\n", "      <td>22.705</td>\n", "      <td>0</td>\n", "      <td>21984.47061</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>32</td>\n", "      <td>28.880</td>\n", "      <td>0</td>\n", "      <td>3866.85520</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   age     bmi  children      charges  sex_female  sex_male  smoker_no  \\\n", "0   19  27.900         0  16884.92400        True     False      False   \n", "1   18  33.770         1   1725.55230       False      True       True   \n", "2   28  33.000         3   4449.46200       False      True       True   \n", "3   33  22.705         0  21984.47061       False      True       True   \n", "4   32  28.880         0   3866.85520       False      True       True   \n", "\n", "   smoker_yes  region_northeast  region_northwest  region_southeast  \\\n", "0        True             False             False             False   \n", "1       False             False             False              True   \n", "2       False             False             False              True   \n", "3       False             False              True             False   \n", "4       False             False              True             False   \n", "\n", "   region_southwest  \n", "0              True  \n", "1             False  \n", "2             False  \n", "3             False  \n", "4             False  "]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["# Turn all categories into numbers\n", "insurance_one_hot = pd.get_dummies(insurance)\n", "insurance_one_hot.head() # view the converted columns"]}, {"cell_type": "code", "execution_count": 6, "id": "5c0801d3", "metadata": {}, "outputs": [], "source": ["# Create X & y values\n", "X = insurance_one_hot.drop(\"charges\", axis=1)\n", "y = insurance_one_hot[\"charges\"]"]}, {"cell_type": "code", "execution_count": 7, "id": "927def7f", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>age</th>\n", "      <th>bmi</th>\n", "      <th>children</th>\n", "      <th>sex_female</th>\n", "      <th>sex_male</th>\n", "      <th>smoker_no</th>\n", "      <th>smoker_yes</th>\n", "      <th>region_northeast</th>\n", "      <th>region_northwest</th>\n", "      <th>region_southeast</th>\n", "      <th>region_southwest</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>19</td>\n", "      <td>27.900</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>18</td>\n", "      <td>33.770</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>28</td>\n", "      <td>33.000</td>\n", "      <td>3</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>33</td>\n", "      <td>22.705</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>32</td>\n", "      <td>28.880</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   age     bmi  children  sex_female  sex_male  smoker_no  smoker_yes  \\\n", "0   19  27.900         0        True     False      False        True   \n", "1   18  33.770         1       False      True       True       False   \n", "2   28  33.000         3       False      True       True       False   \n", "3   33  22.705         0       False      True       True       False   \n", "4   32  28.880         0       False      True       True       False   \n", "\n", "   region_northeast  region_northwest  region_southeast  region_southwest  \n", "0             False             False             False              True  \n", "1             False             False              True             False  \n", "2             False             False              True             False  \n", "3             False              True             False             False  \n", "4             False              True             False             False  "]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["# View features\n", "X.head()"]}, {"cell_type": "code", "execution_count": 10, "id": "8bf0406c", "metadata": {}, "outputs": [], "source": ["# Create training and test sets\n", "from sklearn.model_selection import train_test_split\n", "\n", "X_train, X_test, y_train, y_test = train_test_split(\n", "    X, y, test_size=0.2, random_state=42\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "75ad5c1b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Epoch 1/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m1s\u001b[0m 3ms/step - loss: 8662.1533 - mae: 8662.1533    \n", "Epoch 2/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7686.7383 - mae: 7686.7383 \n", "Epoch 3/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 3ms/step - loss: 7413.8940 - mae: 7413.8940\n", "Epoch 4/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7487.7173 - mae: 7487.7173 \n", "Epoch 5/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7669.2202 - mae: 7669.2202 \n", "Epoch 6/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7803.8945 - mae: 7803.8945 \n", "Epoch 7/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7494.5503 - mae: 7494.5503 \n", "Epoch 8/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7720.9160 - mae: 7720.9160 \n", "Epoch 9/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7731.8496 - mae: 7731.8496\n", "Epoch 10/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7505.3916 - mae: 7505.3916\n", "Epoch 11/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7744.7876 - mae: 7744.7876 \n", "Epoch 12/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7632.8374 - mae: 7632.8374\n", "Epoch 13/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7742.8677 - mae: 7742.8677\n", "Epoch 14/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7522.9087 - mae: 7522.9087 \n", "Epoch 15/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7517.6777 - mae: 7517.6777 \n", "Epoch 16/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7298.8188 - mae: 7298.8188 \n", "Epoch 17/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7647.8901 - mae: 7647.8901 \n", "Epoch 18/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7427.3613 - mae: 7427.3613 \n", "Epoch 19/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7666.6245 - mae: 7666.6245 \n", "Epoch 20/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7438.6401 - mae: 7438.6401 \n", "Epoch 21/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7661.9854 - mae: 7661.9854 \n", "Epoch 22/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7587.4878 - mae: 7587.4878\n", "Epoch 23/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7235.1729 - mae: 7235.1729\n", "Epoch 24/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7322.4336 - mae: 7322.4336 \n", "Epoch 25/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7518.7285 - mae: 7518.7285 \n", "Epoch 26/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7602.6353 - mae: 7602.6353 \n", "Epoch 27/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7433.4316 - mae: 7433.4316 \n", "Epoch 28/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7337.1104 - mae: 7337.1104 \n", "Epoch 29/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7672.1855 - mae: 7672.1855 \n", "Epoch 30/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7553.4897 - mae: 7553.4897 \n", "Epoch 31/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7599.2896 - mae: 7599.2896\n", "Epoch 32/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7655.0840 - mae: 7655.0840 \n", "Epoch 33/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7447.8545 - mae: 7447.8545\n", "Epoch 34/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7588.4692 - mae: 7588.4692 \n", "Epoch 35/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7580.9849 - mae: 7580.9849 \n", "Epoch 36/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7659.3042 - mae: 7659.3042 \n", "Epoch 37/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7439.1455 - mae: 7439.1455 \n", "Epoch 38/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7537.2632 - mae: 7537.2632 \n", "Epoch 39/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7288.0791 - mae: 7288.0791 \n", "Epoch 40/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7299.9258 - mae: 7299.9258 \n", "Epoch 41/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7592.4419 - mae: 7592.4419 \n", "Epoch 42/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7354.3037 - mae: 7354.3037 \n", "Epoch 43/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7464.2432 - mae: 7464.2432 \n", "Epoch 44/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7482.2373 - mae: 7482.2373 \n", "Epoch 45/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7363.8267 - mae: 7363.8267 \n", "Epoch 46/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7140.5708 - mae: 7140.5708 \n", "Epoch 47/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7531.3564 - mae: 7531.3564 \n", "Epoch 48/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7437.3306 - mae: 7437.3306 \n", "Epoch 49/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7440.0278 - mae: 7440.0278 \n", "Epoch 50/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7337.6318 - mae: 7337.6318\n", "Epoch 51/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7441.9087 - mae: 7441.9087\n", "Epoch 52/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7248.6001 - mae: 7248.6001 \n", "Epoch 53/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7453.7642 - mae: 7453.7642 \n", "Epoch 54/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7466.2959 - mae: 7466.2959 \n", "Epoch 55/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7392.5054 - mae: 7392.5054 \n", "Epoch 56/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7457.8110 - mae: 7457.8110 \n", "Epoch 57/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7497.9458 - mae: 7497.9458\n", "Epoch 58/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7399.3042 - mae: 7399.3042\n", "Epoch 59/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7259.5312 - mae: 7259.5312 \n", "Epoch 60/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7092.9927 - mae: 7092.9927 \n", "Epoch 61/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7360.9707 - mae: 7360.9707\n", "Epoch 62/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7436.8071 - mae: 7436.8071 \n", "Epoch 63/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7394.8823 - mae: 7394.8823 \n", "Epoch 64/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 6991.1899 - mae: 6991.1899 \n", "Epoch 65/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7417.0654 - mae: 7417.0654 \n", "Epoch 66/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7403.8481 - mae: 7403.8481\n", "Epoch 67/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7418.3013 - mae: 7418.3013 \n", "Epoch 68/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7363.2476 - mae: 7363.2476 \n", "Epoch 69/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7288.1284 - mae: 7288.1284 \n", "Epoch 70/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7305.5591 - mae: 7305.5591 \n", "Epoch 71/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7067.1470 - mae: 7067.1470 \n", "Epoch 72/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7223.0142 - mae: 7223.0142\n", "Epoch 73/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7295.9048 - mae: 7295.9048 \n", "Epoch 74/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7431.6440 - mae: 7431.6440 \n", "Epoch 75/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7511.1890 - mae: 7511.1890 \n", "Epoch 76/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7175.2397 - mae: 7175.2397 \n", "Epoch 77/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7263.6421 - mae: 7263.6421 \n", "Epoch 78/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7478.1147 - mae: 7478.1147 \n", "Epoch 79/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7372.8647 - mae: 7372.8647\n", "Epoch 80/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7349.7407 - mae: 7349.7407 \n", "Epoch 81/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7273.1831 - mae: 7273.1831 \n", "Epoch 82/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7416.2310 - mae: 7416.2310 \n", "Epoch 83/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7348.8418 - mae: 7348.8418 \n", "Epoch 84/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7379.8623 - mae: 7379.8623\n", "Epoch 85/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7083.1753 - mae: 7083.1753\n", "Epoch 86/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7355.1191 - mae: 7355.1191 \n", "Epoch 87/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7420.7598 - mae: 7420.7598 \n", "Epoch 88/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7124.1348 - mae: 7124.1348 \n", "Epoch 89/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7363.8872 - mae: 7363.8872 \n", "Epoch 90/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7421.6719 - mae: 7421.6719 \n", "Epoch 91/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7130.2896 - mae: 7130.2896 \n", "Epoch 92/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7339.2466 - mae: 7339.2466 \n", "Epoch 93/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7170.7617 - mae: 7170.7617 \n", "Epoch 94/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7374.8262 - mae: 7374.8262 \n", "Epoch 95/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7224.6367 - mae: 7224.6367 \n", "Epoch 96/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 3ms/step - loss: 7380.3667 - mae: 7380.3667\n", "Epoch 97/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7309.7026 - mae: 7309.7026 \n", "Epoch 98/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7225.3452 - mae: 7225.3452\n", "Epoch 99/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7403.7422 - mae: 7403.7422 \n", "Epoch 100/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7314.1909 - mae: 7314.1909\n"]}, {"data": {"text/plain": ["<keras.src.callbacks.history.History at 0x284ca48e120>"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["# Set random seed\n", "tf.random.set_seed(42)\n", "\n", "# Create a new model (same as model)\n", "insurance_model = tf.keras.Sequential([\n", "  tf.keras.layers.Dense(1),\n", "  tf.keras.layers.Dense(1)\n", "])\n", "\n", "# Compile the model\n", "insurance_model.compile(loss=tf.keras.losses.mae,\n", "                        optimizer=tf.keras.optimizers.SGD(),\n", "                        metrics=['mae'])\n", "\n", "# Fit the model\n", "insurance_model.fit(X_train, y_train, epochs=100)\n", "     "]}, {"cell_type": "code", "execution_count": 12, "id": "35dac797", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[1m9/9\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 4ms/step - loss: 8086.0737 - mae: 8086.0737  \n"]}, {"data": {"text/plain": ["[8086.07373046875, 8086.07373046875]"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "# Check the results of the insurance model\n", "insurance_model.evaluate(X_test, y_test)"]}, {"cell_type": "markdown", "id": "74b1fa04", "metadata": {}, "source": ["- Increasing the number of layers (2 -> 3).\n", "- Increasing the number of units in each layer (except for the output layer).\n", "- Changing the optimizer (from SGD to Adam)."]}, {"cell_type": "code", "execution_count": 13, "id": "b0e24dcb", "metadata": {}, "outputs": [], "source": ["# Set random seed\n", "tf.random.set_seed(42)\n", "\n", "# Add an extra layer and increase number of units\n", "insurance_model_1 = tf.keras.Sequential([\n", "  tf.keras.layers.Dense(100), # 100 units\n", "  tf.keras.layers.Dense(10), # 10 units\n", "  tf.keras.layers.Dense(1) # 1 unit (important for output layer)\n", "])\n", "\n", "# Compile the model\n", "insurance_model_1.compile(loss=tf.keras.losses.mae,\n", "                          optimizer=tf.keras.optimizers.<PERSON>(), # <PERSON> works but SGD doesn't \n", "                          metrics=['mae'])\n", "\n", "# Fit the model and save the history (we can plot this)\n", "history = insurance_model_1.fit(X_train, y_train, epochs=100, verbose=0)"]}, {"cell_type": "code", "execution_count": 14, "id": "f0cf7d51", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[1m9/9\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 5ms/step - loss: 4856.7310 - mae: 4856.7310  \n"]}, {"data": {"text/plain": ["[4856.73095703125, 4856.73095703125]"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["# Evaluate our larger model\n", "insurance_model_1.evaluate(X_test, y_test)"]}, {"cell_type": "code", "execution_count": 15, "id": "527cd18b", "metadata": {}, "outputs": [{"data": {"image/png": "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**********************************+R0/f/58E8Tmzp2ro2ZPOYDBacqUKda1117r3u/o6LDS0tKs+++/39G6vE15ebn956H18ccfm/3q6morMDDQevXVV91ttmzZYtosXbrUwUo9V11dnTVy5Ehr4cKF1mmnnWbdcMMN5nXOdd+5/fbbrZNPPvmQxzs7O62UlBTroYcecr9mn//g4GDrpZdeGqAqvcM555xj/fCHP+zx2oUXXmjNnj3bPOdc9x37d8G8efPc+705t5s3bzZft3LlSnebt99+23K5XFZxcfFR1UNP0yDV2tqq1atXm27H/de3s/eXLl3qaG3epqamxmzj4uLM1j7vdu/T/ud+9OjRyszM5NwfIbtn75xzzulxTm2c677zxhtvaNKkSfre975nLjtPnDhRf/nLX9zH8/PzVVpa2uNc2+tt2Zf9OdeHZ/r06frggw+0bds2s79+/Xp99tlnOuuss8w+57r/9Obc2lv7kpz976Gb3d7+DLV7po4GC/YOUnv37jXXzZOTk3u8bu9v3brVsbq8TWdnpxlfY1/WOOaYY8xr9j/IoKAg84/uy+fePobD8/LLL5vLy/bluS/jXPednTt3mktG9iX9X/ziF+Z8X3/99eb8zpkzx30+D/Y7hXN9eO644w7V1taagO/v729+V//ud78zl4RsnOv+05tza2/tPxz2FxAQYP4wPtrzT2iCfL0H5PPPPzd/JaLvFRUV6YYbbjDjCuybGdC/fwDYf1nfd999Zt/uabL/27bHfdihCX3nn//8p1588UX94x//0Lhx47Ru3Trzx5c9cJlz7d24PDdIJSQkmL9gvnwXkb2fkpLiWF3e5LrrrjMDBBctWqQhQ4a4X7fPr315tLq6ukd7zv3hsy+/2TcuHH/88eYvPfthD/a2B3Haz+2/DjnXfcO+k2js2LE9XhszZowKCwvN8+7zye+Uo3frrbea3qZLL73U3KH4gx/8wNzQYN+Za+Nc95/enFt7++Ubptrb280ddUd7/glNg5TdpX7CCSeY6+b7/yVp70+bNs3R2jydPbbQDkzz5s3Thx9+aG4b3p993u07kPY/9/aUBPaHD+f+8JxxxhnauHGj+Uu8+2H3htiXMbqfc677hn2J+ctTZ9hjboYOHWqe2/+d2x8Y+59r+xKTPcaDc314GhsbzfiY/dl/5Nq/o22c6/7Tm3Nrb+0/xOw/2rrZv+vt/3/ssU9H5aiGkaNfvfzyy+aOgOeff97cDTB37lwrJibGKi0tdbo0j/aTn/zEio6Otj766CNrz5497kdjY6O7zY9//GMrMzPT+vDDD61Vq1ZZ06ZNMw8cvf3vnrNxrvvGihUrrICAAOt3v/udlZeXZ7344otWWFiY9fe//93d5oEHHjC/Q15//XVrw4YN1nnnnWdlZWVZTU1NjtbuaebMmWOlp6db8+fPt/Lz863//Oc/VkJCgnXbbbe523Cuj+5u27Vr15qHHVP+8Ic/mOcFBQW9PrezZs2yJk6caC1fvtz67LPPzN27l112mXW0CE2D3J/+9CfzgRIUFGSmIFi2bJnTJXk8+x/hwR7PPfecu439j++nP/2pFRsbaz54LrjgAhOs0PehiXPdd958803rmGOOMX9sjR492nrmmWd6HLdv177rrrus5ORk0+aMM86wcnNzHavXU9XW1pr/hu3fzSEhIdbw4cOtX/7yl1ZLS4u7Def6yC1atOigv6PtsNrbc1tZWWlCUkREhBUVFWVdeeWVJowdLZf9P0fXVwUAAOD9GNMEAADQC4QmAACAXiA0AQAA9AKhCQAAoBcITQAAAL1AaAIAAOgFQhMAAEAvEJoAAAB6gdAEAH3ko48+ksvlOmABYgDegdAEAADQC4QmAACAXiA0AfAanZ2duv/++5WVlaXQ0FAdd9xx+te//tXj0tmCBQt07LHHKiQkRCeeeKI+//zzHu/x73//W+PGjVNwcLCGDRumhx9+uMfxlpYW3X777crIyDBtRowYof/93//t0Wb16tWaNGmSwsLCNH36dOXm5rqPrV+/Xt/4xjcUGRmpqKgonXDCCVq1alW/nhcAfYPQBMBr2IHpr3/9q55++mlt2rRJN910k77//e/r448/dre59dZbTRBauXKlEhMTde6556qtrc0ddi6++GJdeuml2rhxo+655x7dddddev75591ff8UVV+ill17SY489pi1btujPf/6zIiIietTxy1/+0nwPOwwFBATohz/8ofvY7NmzNWTIEPP97e93xx13KDAwcEDOD4CjZAGAF2hubrbCwsKsJUuW9Hj9qquusi677DJr0aJFlv0r7+WXX3Yfq6ystEJDQ61XXnnF7F9++eXWt771rR5ff+utt1pjx441z3Nzc817LFy48KA1dH+P999/3/3aggULzGtNTU1mPzIy0nr++ef78CcHMFDoaQLgFbZv367GxkZ961vfMj0/3Q+752nHjh3udtOmTXM/j4uLU05Ojukxstnbk046qcf72vt5eXnq6OjQunXr5O/vr9NOO+0ra7Ev/3VLTU012/LycrO9+eabdfXVV2vGjBl64IEHetQGYHAjNAHwCvX19WZrj1myw033Y/Pmze5xTUfLHifVG/tfbrPHUXWPt7LZl/zsS4fnnHOOPvzwQ40dO1bz5s3rk/oA9C9CEwCvYIcPe2B2YWGhGZy9/8MetN1t2bJl7udVVVXatm2bxowZY/bt7eLFi3u8r70/atQo08M0fvx4E372HyN1JOz3s8dbvffee7rwwgv13HPPHdX7ARgYAQP0fQCgX9l3o91yyy0mjNjB5uSTT1ZNTY0JPfZdakOHDjXtfvOb3yg+Pl7JyclmwHZCQoLOP/98c+znP/+5Jk+erHvvvVeXXHKJli5dqscff1xPPvmkOW7fTTdnzhwzsNseCG7fnVdQUGAuvdkDyL9OU1OTGYj+3e9+19zht3v3bjMg/KKLLurnswOgTwzY6CkA6GednZ3WI488YuXk5FiBgYFWYmKiNXPmTOvjjz92D9J+8803rXHjxllBQUHWlClTrPXr1/d4j3/9619m4Lf99ZmZmdZDDz3U47g9oPumm26yUlNTzXuMGDHCevbZZ82x7u9RVVXlbr927VrzWn5+vtXS0mJdeumlVkZGhvnatLQ067rrrnMPEgcwuLns/+mb+AUAg5c9T5M9P5J9SS4mJsbpcgB4IMY0AQAA9AKhCQAAoBe4PAcAANAL9DQBAAD0AqEJAACgFwhNAAAAvUBoAgAA6AVCEwAAQC8QmgAAAHqB0AQAANALhCYAAAB9vf8P1qPzD/7bj7EAAAAASUVORK5CYII=", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Plot history (also known as a loss curve)\n", "pd.<PERSON><PERSON><PERSON><PERSON>(history.history).plot()\n", "plt.ylabel(\"loss\")\n", "plt.xlabel(\"epochs\");"]}, {"cell_type": "code", "execution_count": 17, "id": "4013a24e", "metadata": {}, "outputs": [], "source": ["# Try training for a little longer (100 more epochs)\n", "history_2 = insurance_model_1.fit(X_train, y_train, epochs=100, verbose=0)"]}, {"cell_type": "code", "execution_count": 18, "id": "7612a900", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[1m9/9\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 3ms/step - loss: 3405.3672 - mae: 3405.3672 \n"]}, {"data": {"text/plain": ["(3405.3671875, 3405.3671875)"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["# Evaluate the model trained for 200 total epochs\n", "insurance_model_2_loss, insurance_model_2_mae = insurance_model_1.evaluate(X_test, y_test)\n", "insurance_model_2_loss, insurance_model_2_mae"]}, {"cell_type": "code", "execution_count": 19, "id": "0e4904bd", "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAkQAAAGwCAYAAABIC3rIAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjYsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvq6yFwwAAAAlwSFlzAAAPYQAAD2EBqD+naQAAUStJREFUeJzt3Ql4VOXdNvB7ksm+7wtZIRBIIKyyaXEBAUWrFduiIFRF1KJFUECqUKtWKNaqiLuvYj+xuIKIiqAIyL5DCBAChOz7MtmXmTnf9TxxpgmbIUxyZrl/13Xec2bOyeSf09fk5tmORlEUBUREREQOzEntAoiIiIjUxkBEREREDo+BiIiIiBweAxERERE5PAYiIiIicngMREREROTwGIiIiIjI4WnVLsAWGI1G5Ofnw8fHBxqNRu1yiIiIqB3EUovV1dWIjIyEk9Ol24AYiNpBhKHo6Gi1yyAiIqIOyMnJQVRU1CWvYSBqB9EyZLqhvr6+apdDRERE7VBVVSUbNEx/xy+FgagdTN1kIgwxEBEREdmW9gx34aBqIiIicngMREREROTwGIiIiIjI4XEMERERkZUwGAxobm5Wuwyb4urq+qtT6tuDgYiIiMgK1sspLCxEZWWl2qXYHBGG4uPjZTC6EgxEREREKjOFodDQUHh6enIR4MtcOLmgoAAxMTFXdN8YiIiIiFTuJjOFoaCgILXLsTkhISEyFOn1eri4uHT4cziomoiISEWmMUOiZYgun6mrTATLK8FAREREZAXYTabufWMgIiIiIofHQEREREQOj4GIiIiIOuS6667DY489BnvAQKSyqsoynDq8Xe0yiIiIHBqn3avo9JEdiP/iZug1PlD6ZUFjgZU2iYiI6PLxL7CKonoNQBO0CEQVck4dUbscIiKyklWr65r0qmyKonS47oqKCkydOhUBAQFyCYGbbroJGRkZ5vNZWVm49dZb5XkvLy8kJyfj22+/NX/t5MmT5ZpCHh4e6NmzJz744AN0JbYQqcjN3RPH3HojqSkVhambEdNrgNolERGRyuqbDUha9L0q3/vYs+Pg6dqxaPCnP/1JBqC1a9fC19cX8+fPx80334xjx47JBRNnzpyJpqYmbN26VQYi8b63t7f82oULF8rX3333HYKDg3Hq1CnU19ejKzEQqUwXMhjIS4UmZ5fapRAREXWIKQht374dI0eOlO+tXLkS0dHRWLNmDX7/+98jOzsbEydORL9+/eT57t27m79enBs4cCCGDBkiX8fFxaGrMRCpzDPhGiBvBSJ0h9QuhYiIrICHi7NsqVHre3fE8ePHodVqMWzYMPN74jEkiYmJ8pzwl7/8BQ8//DA2bNiAMWPGyHCUkpIiz4n3xesDBw5g7NixuP32283BqqtwDJHK4gfeAKOiQZRSgNLCbLXLISIiK1h5WXRbqbFpOnG17OnTp+PMmTO45557kJqaKluDXnvtNXlOjDcSY4xmz54tn0s2evRoPPHEE+hKDEQq8/UPQqa2pWkw+9AmtcshIiK6bH369JEPV929e7f5vbKyMqSnpyMpKcn8nuhCe+ihh/Dll1/i8ccfx7vvvms+JwZUT5s2DR999BFeeeUVvPPOO+hKDERWoDRgoNw3neF6REREZHt69uyJ2267DQ888AC2bduGw4cPY8qUKejWrZt8XxALOH7//ffIzMyUXWM//fSTDFLCokWL8NVXX8nB1GlpaVi3bp35XFdhILICzvEt/aRBZQfULoWIiKhDxDT5wYMH45ZbbsGIESPkFH4xrV7MMDM9jV7MNBNBZ/z48ejVqxfeeOMN8xPrFyxYIMcUjRo1Cs7Ozli1ahW6kka5kkUHHERVVRX8/Pyg0+nkVEJLK8o9jbD3BsGgaFD/eCa8fQMs/j2IiMg6NTQ0yFaT+Ph4uLu7q12OXd2/y/n7zRYiKxAW1QMFCIGzRkHmoa1ql0NERORwGIisRJ5vf7mvyfhZ7VKIiIgcDgORlTBEDZd7n+K9apdCRETkcBiIrERYv+vlvnvDcTQ3NapdDhERkUNhILISMb0GQgcveGoakXmUj/EgIiLqSgxEVsLJ2RmZHi3Pdyk/vkXtcoiIiBwKA5EVqY8YKveu+XvULoWIiMihMBBZkYDE38h9bO0RKEaj2uUQERE5DAYiKxLf/xo0Ki4Igg65Z9LULoeIiMhhMBBZETd3T5xx7SWPC4/wQa9ERERdhYHIylSGDJZ7JZszzYiIiLoKA5GV8YgfIfehVUfVLoWIiOiSrrvuOjz66KPySfYBAQEICwvDu+++i9raWtx7773w8fFBQkICvvvuO/MDXu+//3753DEPDw8kJibi1VdfPe9z33vvPfkQWPFsst69e5sfAtuZtJ3+HeiyRKeMArYDMYYcVFWWwdc/SO2SiIioK4lnrjfXqfO9XTwBjeayvuTDDz/EvHnzsGfPHnzyySd4+OGHsXr1avzud7/DX//6V7z88su45557kJ2dLZ98HxUVhc8++wxBQUHYsWMHZsyYgYiICPzhD3+Qn7dy5UosWrQIy5cvx8CBA3Hw4EE88MAD8PLywrRp0zrpB+fT7q3iaffnyvt7L3RTipB6wwr0G/W7Tv9+RERkRU9rb6oFXohUp5i/5gOuXpfVQiRafX7+ueU5nOJY/L2844478J///Ee+V1hYKAPPzp07MXx4y2OqWnvkkUfkNZ9//rl8LVqUnnvuOdx1113ma55//nl8++23MkB11tPu2UJkhQp8+qFbVRFqTu8CGIiIiMiKpaSkmI+dnZ1ly0+/fi0LDQuiG00oLi6W+9dffx3vv/++bDGqr69HU1MTBgwYIM+JrrbTp0/LbjXRKmSi1+tlsOlMVhOIlixZggULFmDWrFl45ZVX5HsiMc6dOxcbN25EdXW17Gt86qmnMHHiRPPXlZeXy/7Lr7/+Gk5OTvKc6I/09vY2X3PkyBHMnDkTe/fuRUhIiLxeNO9ZK33EYKDqB3gW7Ve7FCIiUqPbSrTUqPW9L5PoBmtNo9G0eU+8FoxGI1atWoUnnngCL730EkaMGCHHGL344ovYvXu3vKampkbuxTikYcOGtflcEbbsPhCJoPL222+3SZnC1KlTUVlZibVr1yI4OBgff/yx7GPct2+f7FcUJk+ejIKCAhmampub5SAu0R8prjU1l40dOxZjxozBW2+9hdTUVNx3333w9/eX11mjoN7XAOn/RFzDMblAo8aJY9+JiByGCBCX0W1lS7Zv346RI0fiz3/+s/k90SLUujUpMjISZ86ckX/fu5Lqf2lFGhQ/tEiDYoR6a6KvULTmDB06FN27d8fTTz8tg8z+/S0tJ8ePH8f69evlaHSRJK+55hq89tprMoHm5+ebB2eJ5jjRPJecnIxJkybhL3/5C/79739ftKbGxkYZpFpvXSk2aSgaFBf4oRa5p1O79HsTERF1lp49e8pGje+//x4nT57EwoULZaNIa3//+9+xePFiLFu2TF4jGjI++OCDS/7dtotAJLqyJkyYIFtwziVSpBixLrrFTE1tYvCUGMQliAFaIiANGTLE/DXic0TXman5TVwzatQouLq6mq8ZN24c0tPTUVFRccGaxP8Qoq/StEVHR6Mrubq5I9O1pzwuTNvWpd+biIioszz44INywPUf//hH2ZBRVlbWprVImD59umzoECFIjEW69tprsWLFCjlo2m67zETAOXDgwHnp0OTTTz+VN00M0NJqtfD09JRT+cQIdNMYo9DQ0DZfI64LDAyU50zXnHsTTQO8xLlzW6UEMZZpzpw55teihairQ5EuaCBQeAzGHPGg15ld+r2JiIjaY/Pmzee9d/bs2fPeaz2hXQQdsZ3bENHa3XffLbeupFogysnJkQOoxdifc6fJmYimNDGG6IcffpBjiNasWSPHEInpfa1HsFuam5ub3NTkGjcUKFyJ4MojqtZBRETkCFQLRGIckJiCN2jQIPN7Yv2CrVu3ysWYRJeW2B89elSO/RH69+8vw5CYsicGSIeHh5un8bWemie62MQ5QeyLioraXGN6bbrGGkX1uxbYBcTpM1FXo4Ond+dONyQiInJkqo0hGj16tBwodejQIfMmxgKJAdbiuK6uZZVOMR7o3Gl3YjyRIKbsiRYk0yBrYdOmTfK8abqeuEaELDEDzUS0Sokp/BfqLrMWod3iUYQgOGsUZB7mOCIiIiK7DERi7YG+ffu22cSy3GK8kDgWzy4RY4XEACyxHLiYlifWLRBh5vbbb5efIZ5zMn78eLl4k7hGTOcTK16KmWRi2p4g+iDFgGqxyFNaWpocpC3WKWo9Rsha5Xn3lfuqU+evzElERER2NMvsYsSiTmKZbrGQ4q233irXKBLLgItnptx8883m68S0ehGeRIuTeF9MvX/nnXfM58UssQ0bNshlvQcPHozHH39cPiPFWtcgaq1JLNAIwL3ogNqlEBFRJ+OTtNS9b3yWmRU+y8zkxJ6N6P3tnSiFP4IWZXKBRiIiOyTGz4r1dsSsadFLQpdH/G0Waw+KXqVzV83ms8zsRFy/kWj6xhnBmkrkZ51EZHxvtUsiIiILE2NjxZp6pklCYokZ0+Mu6NLEmOGSkhJ5z8SyO1eCgciKuXt44aRLD/TSn0R+2lYGIiIiO2Wa9XzuzGn6dWLyVUxMzBWHSAYiK1ce0B8oOQl9llig0frHPRER0eUTf8wjIiJkt1nrWdH068TEqXNnpHcEA5GV08YNA0o+Q2DFYbVLISKiLug+6+ynutOFcZSulYtMHiX38c2n0VBXo3Y5REREdomByMpFxPSUs8xcNAacPdbywFoiIiKyLAYiKyem2ud69pHHlRm71C6HiIjILjEQ2YD6kP5yry3gAo1ERESdgYHIBnh3b3kuW1j1MbVLISIisksMRDYgpu/Vch+t5ENXVqR2OURERHaHgcgG+AWFIUfT8rDa7KPb1S6HiIjI7jAQ2YginyS5rznDmWZERESWxkBkI/ThA+Xeo4QLNBIREVkaA5GN8O85XO6j6o5DMRrVLoeIiMiuMBDZiLjk4WhWnBGMShTlnla7HCIiIrvCQGQj3D29kaWNk8f5advULoeIiMiuMBDZkDL/fnLflLVX7VKIiIjsCgORDdFEDZZ7n/IjapdCRERkVxiIbEho75FyH994Ega9Xu1yiIiI7AYDkQ2J7jkAtYo7PDWNyD55UO1yiIiI7AYDkQ1x1mpx1q2XPC49sUPtcoiIiOwGA5GNqQpKkXtj3n61SyEiIrIbDEQ2xi32KrkPqjyqdilERER2g4HIxkQmXyP3sfqzaKirUbscIiIiu8BAZGPCunVHKfzhojHgbNoutcshIiKyCwxENkbj5IQcz5Yn31dmMBARERFZAgORDWoI7S/32oIDapdCRERkFxiIbJBXXMvA6rCa42qXQkREZBcYiGxQdHLLitXRSj6qdeVql0NERGTzGIhsUEBIBAoRIo+zj+5UuxwiIiKbx0Bko/K9est9dSaffE9ERHSlGIhsVFNIy4rV2iI++Z6IiOhKMRDZKM+4wXLPgdVERERXjoHIDgZWV1WWqV0OERGRTWMgsuGB1QW/DKzO4YrVREREV4SByIYVePWR++rMPWqXQkREZNMYiGxYY2g/uXfhwGoiIqIrwkBkw7zjhsh9aM0JtUshIiKyaQxENowDq4mIiCyDgciG+QeHI18TKo+z03aoXQ4REZHNYiCycYWeLStW15zhitVEREQ2H4iWLFkCjUaDxx57rM37O3fuxA033AAvLy/4+vpi1KhRqK+vN58vLy/H5MmT5Tl/f3/cf//9qKmpafMZR44cwW9+8xu4u7sjOjoaS5cuhb1oDG1ZsdqlmAOriYiIbDoQ7d27F2+//TZSUlr+uLcOQ+PHj8fYsWOxZ88eed0jjzwCJ6f/lS3CUFpaGjZu3Ih169Zh69atmDFjhvl8VVWV/PrY2Fjs378fL774Ip555hm88847sAfe8S0Dq8M4sJqIiKjDNIqiKFCRaM0ZNGgQ3njjDTz//PMYMGAAXnnlFXlu+PDhuPHGG/Hcc89d8GuPHz+OpKQkGZSGDGkJBuvXr8fNN9+M3NxcREZG4s0338RTTz2FwsJCuLq6ymuefPJJrFmzBidOtC9EiFDl5+cHnU4nW6KsSWVpIfyXJ8pj3azT8AsIVrskIiIiq3A5f79VbyGaOXMmJkyYgDFjxrR5v7i4GLt370ZoaChGjhyJsLAwXHvttdi2bVubFiTRTWYKQ4L4HNGCJL7WdI3oZjOFIWHcuHFIT09HRUXFBWtqbGyUN7H1Zt0Dq8PkcQ4HVhMREXWIqoFo1apVOHDgABYvXnzeuTNnzsi96N564IEHZMuPaEkaPXo0MjIy5DnR6iMCU2tarRaBgYHynOkaEaZaM702XXMuUY9IlKZNjDuyZoVeLS1ENZn71C6FiIjIJqkWiHJycjBr1iysXLlSDnY+l9FolPsHH3wQ9957LwYOHIiXX34ZiYmJeP/99zu1tgULFsjmNdMmarVmjSG/DKwuOqx2KURERDZJq9Y3FgOcRbeYaPUxMRgMclD08uXLZZeWIMYItdanTx9kZ2fL4/DwcPkZren1ejnzTJwzXVNUVNTmGtNr0zXncnNzk5ut8I4fCmQCYbUcWE1ERGRTLUSi6ys1NRWHDh0yb2IskJg1Jo67d+8uB0WbgpHJyZMn5YwxYcSIEaisrJThymTTpk2ydWnYsGHma0TIam5uNl8jZqSJlqaAgADYg5jk4XIfpRRCV16idjlEREQ2R7VA5OPjg759+7bZxFpDQUFB8lisSTR37lwsW7YMn3/+OU6dOoWFCxfKmWFirSFTa5GYli/GGIlp+du3b5fT8idNmiTDlHD33XfLAdXia8T0/E8++QSvvvoq5syZA3vhFxTGgdVERES22GXWHmKRxoaGBsyePVt2g/Xv31+27vTo0cN8jRiDJEKQaHESs8smTpwoQ5SJGBS9YcMGOZtt8ODBCA4OxqJFi9qsVWQPCj0TEVlbhJqsA8BvblO7HCIiIpui+jpEtsCa1yEy2bXirxh+9nXs8x2DIXO+ULscIiIi1dnUOkRkGR6xA+U+hCtWExERXTYGIjvRrU/LwOpoQx5qqyvVLoeIiMimMBDZieDwaBQjEE4aBTnH+eR7IiKiy8FAZEcKPHrKve4MV6wmIiK6HAxEdqQ+qK/cawqPqF0KERGRTWEgsiOu0S0Dq4OqObCaiIjocjAQ2ZGI3kPlPkafhcaGOrXLISIishkMRHYkPLondPCCi8aAnPQDapdDRERkMxiI7IjGyQk5bi0Dq8tPcaYZERFRezEQ2ZmagGS5Vwo4sJqIiKi9GIjsjDZqgNz7Vx5TuxQiIiKbwUBkZ0J7/TKwuvkMDHq92uUQERHZBAYiOxPVox/qFDd4aJqQm3FY7XKIiIhsAgORnXFydkaWaw95XJKxR+1yiIiIbAIDkR2q8k+Se30eW4iIiIjag4HIDmki+8u9T0Wa2qUQERHZBAYiOxSU0DKwOrrpFBSjUe1yiIiIrB4DkR2KSRyIJkULX9Qh/2y62uUQERFZPQYiO+Ti6oYsbZw8LkrfpXY5REREVo+ByE5V+PaW+8bcQ2qXQkREZPUYiOyUEtEysNqrjAOriYiIfg0DkZ3y6z5Y7iMbMtQuhYiIyOoxENmp2D5DYVA0CEYlSvOz1C6HiIjIqjEQ2SkPLx/kOkfJ47wTu9Uuh4iIyKoxENmxEu9Eua/P5sBqIiKiS2EgsmP6kGS5dy1NVbsUIiIiq8ZAZMe8YlsGVofWnlS7FCIiIqvGQGTHopOGyX2UUoiqyjK1yyEiIrJaDER2zD84HIUIlse5x/eoXQ4REZHVYiCycwWeveS+KnO/2qUQERFZLQYiO9cQ1DKw2rn4qNqlEBERWS0GIjvnHj1A7gOrT6hdChERkdViILJz4YlXyX2MPhuNDXVql0NERGSVGIjsXHh0T+jgBReNATnpB9Quh4iIyCoxENk5jZMTctx6yuPyU/vULoeIiMgqMRA5gBr/PnKvFBxRuxQiIiKrxEDkALTdWgZW++qOq10KERGRVWIgcgDBPYfIfUzTGRgNBrXLISIisjoMRA4gKiEFDYoLvDQNyDvD9YiIiIjOxUDkALQursh2iZfHxSf3ql0OERGR1WEgchAVfi0Dq5vyDqtdChERkdWxmkC0ZMkSaDQaPPbYY+edUxQFN910kzy/Zs2aNueys7MxYcIEeHp6IjQ0FHPnzoVer29zzebNmzFo0CC4ubkhISEBK1asgMMJT5E7r/JjaldCRERkdawiEO3duxdvv/02UlJa/mif65VXXpFh6FwGg0GGoaamJuzYsQMffvihDDuLFi0yX5OZmSmvuf7663Ho0CEZuKZPn47vv/8ejiSgR8vA6m4NGVCMRrXLISIisiqqB6KamhpMnjwZ7777LgICAs47L0LMSy+9hPfff/+8cxs2bMCxY8fw0UcfYcCAAbIV6bnnnsPrr78uQ5Lw1ltvIT4+Xn5Gnz598Mgjj+DOO+/Eyy+/DEcSnTgYBkWDIOhQWpitdjlERERWRfVANHPmTNmCM2bMmPPO1dXV4e6775YBJzw8/LzzO3fuRL9+/RAWFmZ+b9y4caiqqkJaWpr5mnM/W1wj3r+YxsZG+RmtN1vn4eWDHOcoeZx/Ypfa5RAREVkVVQPRqlWrcODAASxevPiC52fPno2RI0fitttuu+D5wsLCNmFIML0W5y51jQg59fX1F/xcUY+fn595i46Ohj0o8WkZWF13ls80IyIisopAlJOTg1mzZmHlypVwd3c/7/zatWuxadMmOX6oqy1YsAA6nc68iVrtgSGsv9y7l6aqXQoREZFVUS0Q7d+/H8XFxXL2l1arlduWLVuwbNkyebxx40acPn0a/v7+5vPCxIkTcd1118lj0Y1WVFTU5nNNr01dbBe7xtfXFx4eHhesTcxGE+dbb/bAr8dVct+t7oTapRAREVmVlpShgtGjRyM1tW1Lxb333ovevXtj/vz5CA4OxoMPPtjmvBgvJAZD33rrrfL1iBEj8I9//EMGKzHlXhBBSgSYpKQk8zXffvttm88R14j3HU1M0jAYvtUgVFMuB1YHh8eoXRIREZFjByIfHx/07du3zXteXl4ICgoyv3+hgdQxMTFy1pgwduxYGXzuueceLF26VI4Xevrpp+VAbdHKIzz00ENYvnw55s2bh/vuu092w3366af45ptv4Gi8fPxx1jkKccYc5B3bwUBERERkLbPMroSzszPWrVsn96LFZ8qUKZg6dSqeffZZ8zUiPInwI1qF+vfvL6ffv/fee3KmmSMq8WlpOas7u1/tUoiIiKyGai1EFyJWlL4UsWL1uWJjY8/rEjuXGHN08ODBK67PHhjC+wO67+HBgdVERET20UJEl8+/OwdWExERnYuByMHEJA+TK1aHoAIl+WfVLoeIiMgqMBA5GE9vP+Q4tyw0mXfs4qt1ExERORIGIgceWF2fxYHVREREAgORow6sFs8348BqIiIiiYHIAfn/smJ1VD0HVhMREQkMRA4oNnm4HFgdjEoOrCYiImIgckweXj7Idm5ZpTovbYfa5RAREamOgchBlfr+MrA6mwOriYiIGIgclPGXgdWepUfULoWIiEh1DEQOyu+XgdXd6k9CMRrVLoeIiEhVDEQOKjZpGPSKU8vA6oIstcshIiJSFQORAw+szjEPrN6udjlERESqYiByYCW/DKxuzD6gdilERESqYiByYEqEacVqDqwmIiLHxkDkwEwDq6MaOLCaiIgcGwORA4v7ZWB1EHQozs9UuxwiIiLVMBA5MHdPb/PA6vxjO9Uuh4iISDUMRA6u1LeP3DfkHFS7FCIiItUwEDk4Q1iK3HuWpqpdChERkW0Fog8//BDffPON+fW8efPg7++PkSNHIiuLi/zZEn/zitXpapdCRERkW4HohRdegIeHhzzeuXMnXn/9dSxduhTBwcGYPXu2pWukThSbPBwGRdOyYnX+WbXLISIiUoW2I1+Uk5ODhIQEebxmzRpMnDgRM2bMwNVXX43rrrvO0jVSJ69YfdY5GnHGbOQd34WQyDi1SyIiIrKNFiJvb2+UlZXJ4w0bNuDGG2+Ux+7u7qivr7dshdTpSnx6y3191n61SyEiIrKdFiIRgKZPn46BAwfi5MmTuPnmm+X7aWlpiItjC4OtMYT1B3Qb4MGB1URE5KA61EIkxgyNGDECJSUl+OKLLxAUFCTf379/P+666y5L10hdtGJ1ZB0HVhMRkWPSKIqiqF2EtauqqoKfnx90Oh18fX1hb2qrK+Hxrzg4aRSUPpSK4PCWxRqJiIgc5e93h1qI1q9fj23btrVpMRowYADuvvtuVFRUdOQjSUVePv7IcY6Sx3nHdqldDhERUZfrUCCaO3euTF1CamoqHn/8cTmOKDMzE3PmzLF0jdQFSrwT5b6OA6uJiMgBdWhQtQg+SUlJ8liMIbrlllvk2kQHDhwwD7Am26IXA6urfoB7yRG1SyEiIrKNFiJXV1fU1dXJ4x9++AFjx46Vx4GBgeaWI7Itvt05sJqIiBxXh1qIrrnmGtk1JhZi3LNnDz755BP5vpiCHxXVMhaFbEtM8nDgeyAMZSgrykVQGP93JCIix9GhFqLly5dDq9Xi888/x5tvvolu3brJ97/77juMHz/e0jVSF/D2DUCOJlIec2A1ERE5mg61EMXExGDdunXnvf/yyy9boiZSSZF3H0RX56M2ax+AO9Uuh4iIyLoDkWAwGORzzI4fPy5fJycn47e//S2cnZ0tWR91IX1YClD9I9xLuGI1ERE5lg4FolOnTsnZZHl5eUhMbJmuvXjxYkRHR+Obb75Bjx49LF0ndQGf+KuAU0B4LQdWExGRY+nQGKK//OUvMvSIp96LqfZiy87ORnx8vDxHtilaDKwGEIESVJYWql0OERGRdQeiLVu2YOnSpXKavYl4ntmSJUvkObJNvv5ByNVEyOOctB1ql0NERGTdgcjNzQ3V1dXnvV9TUyPXKCLbVeTdW+5rM8XAaiIiIsfQoUAkVqaeMWMGdu/eDfFsWLHt2rULDz30kBxYTbarWaxYLUJvyWG1SyEiIrLuQLRs2TI5hmjEiBFwd3eX28iRI5GQkIBXXnnF8lVSl/HpPkzuI2pPqF0KERGRdc8y8/f3x1dffSVnm5mm3ffp00cGIrJtsX1HwPi9BuGaUpQW5iA4PFrtkoiIiKwnEP3aU+x/+ukn8/G///3vyy5EDMhesGABZs2aJVuZysvL8be//Q0bNmyQM9hCQkJw++2347nnnoOfn5/568S5hx9+WH5/b29vTJs2TS4BIFbSNtm8ebOsPy0tTS4N8PTTT+NPf/rTZdfoKCtWZzlHIdaYg9y07QgOn6R2SURERNYTiA4ePNiu6zQazWUXsXfvXrz99ttISUkxv5efny+3f/3rX0hKSkJWVpYcoyTeE48MMS0OOWHCBISHh2PHjh0oKCjA1KlT4eLighdeeEFek5mZKa8RX7ty5Ur8+OOPmD59OiIiIjBu3LjLrtURFPskIVaXg/qzYmA1AxEREdk/jSJGRKtIzEwbNGgQ3njjDTz//PMYMGDARcchffbZZ5gyZQpqa2tlC5B4dpoY4C1CUlhYmLzmrbfewvz581FSUiJnvIljsVjk0aNHzZ8zadIkVFZWYv369Rf8Po2NjXIzqaqqki1LOp0Ovr6+sHe7Vy3GsBNLcNhjKPrP36h2OURERB0i/n6LXqX2/P3u0KBqS5o5c6ZswRkzZsyvXmv6gUzdYTt37kS/fv3MYUgQrT7iBojuMdM15362uEa8fzGiy03cQNMmwpAj8UsYKvdR9elQjEa1yyEiIup0qgaiVatWyVWuRQD5NaWlpXL8kJjub1JYWNgmDAmm1+Lcpa4Roam+vv6C30uMZRLhy7SJFbkdSVzycDQrzgiCDkW5p9Uuh4iIyHof7nqlRMgQA6g3btwop+1figgvohVJjCV65plnOr02sfCk2ByVu4cXTmnjkGA4jfxj2xEe01PtkoiIiOyzhWj//v0oLi6W44dEF5jYxGM/xBpH4lgMmBbEitjjx4+Hj48PVq9eLQdMm4jB1EVFRW0+1/RanLvUNaLrzcPDowt+UttU7pck943Z+9UuhYiIyH4D0ejRo5GamopDhw6ZtyFDhmDy5Mny2NnZWbYMjR07Vg6OXrt27XktSWJhSPEZIliZiBYnEXZEa5LpGjGzrDVxjXifLiFykNz5lB1RuxIiIiL77TITLT59+/Zt856Xl5d8SKx43xSG6urq8NFHH8nXYhPEmkQiMInzIvjcc8898mGzYryQWGNIDNQ2dXmJ6fbLly/HvHnzcN9992HTpk349NNP5cwzurjAnsOAo0BM40k5sFrjpPr4eyIiok5jtX/lxGBr8aw00QIkVsAW6waZNtMgZxGK1q1bJ/eixUdMyRfrED377LPmz4mPj5fhR7QK9e/fHy+99BLee+89rkH0K2L7DEGD4gJf1CH3TMuMPSIiInul+jpE9raOgT1Jf34YEvUnsG/wUgy59UG1yyEiIrLfdYjIelX6J8u9PveA2qUQERF1KgYiuiinqMFy71eeqnYpREREnYqBiC4qtHfLTLzYplMw6PVql0NERNRpGIjooqISUlCruMNT04jsk+17uC8REZEtYiCii3LWanHWrWWV6pL0iz/7jYiIyNYxENElVQemyL2SxxYiIiKyXwxEdEku0S0rVgdUHlW7FCIiok7DQESXFN7narmPaz6DxoY6tcshIiLqFAxEdEmRcYkohy9cNXpkpu5QuxwiIqJOwUBElySeYZbl2fLMucqT29Uuh4iIqFMwENGvaggfIveu+fvULoWIiKhTMBDRr/Lr1TKOKKr2qHzyPRERkb1hIKJf1T3lGjQrzghFOQpzMtQuh4iIyOIYiOhXuXt6I9OlhzzOO7pV7XKIiIgsjoGI2qU8oL/cG87uUrsUIiIii2MgonbRxg2T+8CKw2qXQkREZHEMRNQuUf2uMy/QWF9brXY5REREFsVARO0SFtUDxQiEi8aAzCPb1C6HiIjIohiIqN0LNOZ6tyzQqDvJQERERPaFgYjarSmiZYFG96IDapdCRERkUQxE1G7+idfIfVwdF2gkIiL7wkBE7RbfdwSaFC0CUIW8M8fULoeIiMhiGIio3dzcPXHGtZc8Lji6Re1yiIiILIaBiC5LZWDLAo3GnD1ql0JERGQxDER0Wdy6j5D7kIpDapdCRERkMQxEdFmif1mgMdaQhWpdudrlEBERWQQDEV2W4MhY5GtC4axRcPbwz2qXQ0REZBEMRHTZ8r37yX3NKS7QSERE9oGBiC6bMbZlPaKAfLYQERGRfWAgossWO/w2ue/VfAIVJQVql0NERHTFGIioQw96PeMUByeNgtO71qpdDhER0RVjIKIOKQof1XKQsUHtUoiIiK4YAxF1iF/KBLlPqNoFg16vdjlERERXhIGIOqTX4BtQBU/4owYZBzerXQ4REdEVYSCiDtG6uCLDe6g8rjj8jdrlEBERXREGIuowY8KNch9SwAe9EhGRbWMgog6L/2X6fYLhNErzs9Quh4iIqMMYiKjDgsOjkaHtKY/P7P5K7XKIiIg6jIGIrkhpxLVyrz29Ue1SiIiIOoyBiK5IYP+W6fc9q/eiualR7XKIiIg6hIGIrkjCgFGogC98NPU4ue9HtcshIiKy7UC0ZMkSaDQaPPbYY+b3GhoaMHPmTAQFBcHb2xsTJ05EUVFRm6/Lzs7GhAkT4OnpidDQUMydOxf6cxYK3Lx5MwYNGgQ3NzckJCRgxYoVXfZz2TtnrRanfYfJ46pUTr8nIiLbZBWBaO/evXj77beRkpLS5v3Zs2fj66+/xmeffYYtW7YgPz8fd9xxh/m8wWCQYaipqQk7duzAhx9+KMPOokWLzNdkZmbKa66//nocOnRIBq7p06fj+++/79Kf0a4ljpO78KKf1a6EiIioQzSKoihQUU1NjWy9eeONN/D8889jwIABeOWVV6DT6RASEoKPP/4Yd955p7z2xIkT6NOnD3bu3Inhw4fju+++wy233CKDUlhYmLzmrbfewvz581FSUgJXV1d5/M033+Do0aPm7zlp0iRUVlZi/fr1F6ypsbFRbiZVVVWIjo6WNfn6+nb6PbE1urIieC9LhLNGQc6UbYhO6Kd2SURERBB/v/38/Nr191v1FiLRJSZacMaMGdPm/f3796O5ubnN+71790ZMTIwMRILY9+vXzxyGhHHjxskbkJaWZr7m3M8W15g+40IWL14sb6BpE2GILs4vKAxpHkPkce5P76ldDhER0WVTNRCtWrUKBw4ckAHkXIWFhbKFx9/fv837IvyIc6ZrWoch03nTuUtdI0JTfX39BetasGCBTJOmLScn5wp/UvvXnHKX3PfIW8uHvRIRkc1RLRCJkDFr1iysXLkS7u7usCZi8LVoWmu90aUlXz8JOnghFOVI28ZFGomIyLaoFohEl1hxcbEcP6TVauUmBk4vW7ZMHotWHDFYWoz1aU3MMgsPD5fHYn/urDPT61+7RoQcDw+PTv4pHYe7hxdOBLcMrm7e///ULoeIiMg2AtHo0aORmpoqZ36ZtiFDhmDy5MnmYxcXF/z44//WtklPT5fT7EeMGCFfi734DBGsTDZu3CjDTlJSkvma1p9husb0GWQ5gdfcJ/d9q7ZBV16idjlERETtpoVKfHx80Ldv3zbveXl5yTWHTO/ff//9mDNnDgIDA2XIefTRR2WQETPMhLFjx8rgc88992Dp0qVyvNDTTz8tB2qLbi/hoYcewvLlyzFv3jzcd9992LRpEz799FM584wsKyHlamSujUO88SwObXwfw/44X+2SiIiI2kX1WWaX8vLLL8tp9WJBxlGjRsnury+//NJ83tnZGevWrZN7EZSmTJmCqVOn4tlnnzVfEx8fL8OPaBXq378/XnrpJbz33ntyphlZlsbJCUU9JsrjwIzP1C6HiIjIdtYhsrd1DBxdeXEefF7vBxeNAZl/+AHxSVepXRIRETmoKltah4jsS2BoNxz1aunSLNryf2qXQ0RE1C4MRGRxmoFT5L5n0bdobvrfit9ERETWioGILC752okogx+CoEPali/ULoeIiOhXMRCRxbm4uiEj7GZ57LzvXbXLISIi+lUMRNQpYsY/hmbFGf0aD+DE3h/ULoeIiOiSGIioU0TG98bBwPHyuOnH859VR0REZE0YiKjTdLt1IfSKE1Ia9iF93ya1yyEiIrooBiLqNN2698GBgJZWooYfXlC7HCIiootiIKJO1e23T8tWov4Ne3HywGa1yyEiIrogBiLqVN26J+Og/1h5XL/xH2qXQ0REdEEMRNTpIn4ZS9S/fg9biYiIyCoxEFGni0roi4P+N8rj+o0cS0RERNaHgYi6RPitC2FQNOhfvxsnD2xRuxwiIqI2GIioS0Qn9MMB01iiDc+pXQ4REVEbDETUZSJuXWSeccbVq4mIyJowEFGXjiUyrUvU/MPzapdDRERkxkBEXSrq9md+ecbZQRzb+Z3a5RAREUkMRNSlIuMScSD4Fnms/MQZZ0REZB0YiKjLxd6+CE2KFslNR3B021q1yyEiImIgoq4XHp2AgyG3yWPnLYuhGI1ql0RERA6OgYhU0f2ORWhQXNCn+RiO/rxG7XKIiMjBMRCRKkIi43AofKI8Dtr8JEryz6pdEhEROTAGIlJN4p3PIFcTjkilCLXv3YKKkgK1SyIiIgfFQESqCQiJgNO0tShGIOKMOSh96xZUVZapXRYRETkgBiJSfRp+/V2rUQFf9DScQu7rv0V9bbXaZRERkYNhICLVxSYOQNnvVqEKnkhqPoqM136HpsYGtcsiIiIHwkBEViGh/9XIv+lD1CluSGnYi6PLfg99c5PaZRERkYNgICKr0XvYWJwe/bZctHFQ7VYcXD4ZRoNB7bKIiMgBMBCRVek36nc4ds1r0CtOuEq3AXvfuI8LNxIRUadjICKrM+DGu3Fo6FIYFQ2Gla3B7rf/zFBERESdioGIrNKQCQ9gf/+/y+PhRf/FrndnsfuMiIg6DQMRWa2r7piFXYnz5fGIgv/g8Eu3oFpXrnZZRERkhxiIyKoNv+uv2NP/eTQqLhhYtwPlr/4G2ScPqV0WERHZGQYisnpDf/cosm77XK5oHWvMRcDK8Tj04yq1yyIiIjvCQEQ2odeg6+D00BYcd0mGj6YeKVsfws53Z6G5qVHt0oiIyA4wEJHNCA6PQY8nNmF30O1w0igYkbcCp5dei/yz6WqXRkRENo6BiGyKq5s7hj36IfYP/bd81Edv/XF4r7gO+7/9QO3SiIjIhjEQkU0afPP9qPnTZqRre8MXdRi85zHsffn3bC0iIqIOYSAimxUZl4ju87ZiZ7c/yUUcxcrWIR+MwO5l96AwO0Pt8oiIyIYwEJFNc3F1w4gHXkXGrV8i1W0QXDQGDCtfi8D/G4bdr01DYc4ptUskIiIboFEURVG7CGtXVVUFPz8/6HQ6+Pr6ql0OXcLx3d/DsOkF9G1sWatIPCj2YPAtiL3taYTH9FS7PCIistK/36q2EL355ptISUmRRYptxIgR+O6778znCwsLcc899yA8PBxeXl4YNGgQvvjiizafUV5ejsmTJ8uv9/f3x/3334+ampo21xw5cgS/+c1v4O7ujujoaCxdurTLfkbqWn2GjUPfBVtwbNwqpLmmwFWjl89Da2kxmsoxRkREZH2BKCoqCkuWLMH+/fuxb98+3HDDDbjtttuQlpYmz0+dOhXp6elYu3YtUlNTcccdd+APf/gDDh48aP4MEYbE9Rs3bsS6deuwdetWzJgxo006HDt2LGJjY+X3efHFF/HMM8/gnXfeUeVnpq6RNOImJP/1ZxmMjroNgKvoSiv7CmEfDMORJWNwYP0KNDU2qF0mERFZCavrMgsMDJShRbT0eHt7y1Yk0UpkEhQUhH/+85+YPn06jh8/jqSkJOzduxdDhgyR59evX4+bb74Zubm5iIyMlF//1FNPydYmV1dXec2TTz6JNWvW4MSJE+2qiV1mtu/YrvUw/PRP9Gs8YH6vHL44GTYBwSOnoEe/kdA4cUgdEZE9sZkus9YMBgNWrVqF2tpa2XUmjBw5Ep988onsFjMajfJ8Q0MDrrvuOnl+586dspvMFIaEMWPGwMnJCbt37zZfM2rUKHMYEsaNGydbnioqKi5YS2Njo7yJrTeybUnDx6Pfgp+QO2U7dkZOQwkCEIgqDC/6LxJWT0DBc72x660/4+SBzVCMRrXLJSKiLqaFykRXmAhAIuiIFqHVq1fLVh/h008/xR//+EfZKqTVauHp6SnPJyQkyPOi1Sc0NLTN54nrRCuTOGe6Jj4+vs01YWFh5nMBAQHn1bR48WL8/e9/77SfmdQTldAXUQnLoG/+Fw5t/RLGgyvRp3oXIlGEyMKVwNqVKFwbjLNhN8L/qt8jcdD1bDkiInIAqgeixMREHDp0SDZnff7555g2bRq2bNkiQ9HChQtRWVmJH374AcHBwbKbS4wh+vnnn9GvX79Oq2nBggWYM2eO+bVoIRKDscl+aF1cMWD0JGD0JNTV6HBg22ooaV+hT9V2hGtKEV70X2Ddf1G4ToSjMfDsMxaxKaPgFxiidulERGSPgUh0ZZlafAYPHizHA7366quYN28eli9fjqNHjyI5OVme79+/vwxDr7/+Ot566y05+6y4uLjN5+n1etnFJs4JYl9UVNTmGtNr0zXncnNzkxs5Bk9vPwwa/ydg/J/QUFeDg9vWwJD6ZatwtAoQ22YgyykKxT59Yew2GH7xgxDdewi8fPzV/hGIiMjWA9G5xFghMYanrq5OvhbjgVpzdnaW1wiiq020IInZYyJMCZs2bZLnhw0bZr5GDKpubm6Gi4uLfE/MSBMtUxfqLiPH5u7pjYFjpwBjp5jDkT7ta4RXHUG0ko9YYy5idbmAbj1wDDCu0yDHKQIlXgloDE6GZ8wARCQOQ0hELLvaiIhsiKqzzETX1E033YSYmBhUV1fj448/ljPIvv/+ezlwWnSbRURE4F//+pccRyS6zObOnSun14uZZIL4etHiI1qMROi599575SBr8VmC6IoT4UdMvZ8/f75scbrvvvvw8ssvt5mefymcZUZCRUkBslN/Rv2ZXfAsPYKIhlMIwYUH5lfAF7luPVAbkARt1ACE9hqKqB794OTs3OV1ExE5qqrL+PutaiASU+t//PFHFBQUyILFIo0itNx4443yfEZGhpwiv23bNrnYouhae+KJJ9pMwxfdY4888gi+/vpr2Zo0ceJELFu2TA7Qbr0w48yZM2V3nBiL9Oijj8rv014MRHQxZUW5yE/fi9rsQ9AWpyGo5iSiDTnQas6fqVanuCHHJR46354whiTBO6YfuvUagoCQCFVqJyKyd1W2EohsBQMRXY6G+lrkpB9Axel9UAqOwL/yGGKaz8BD03TB64sRiAKPnqgLTIJb9AAExCTDLzgS/kHhbFEiIroCDEQWxkBEV8qg1yP31BGUnj6ApvyjcK9IR2j9aXRT2g74b02vOKFS4wudcwDKvXvCGDkYgb1GIjZpKFzd3Lu0fiIiW8RAZGEMRNRZaqoqkHt8L3RnD0BTmIqAqhMIMRTCH22fx9dao+KCbG0sqj0i0eTVDZqAWLgFx8E3vDuCoxLg6x/UpT8DEZG1YiCyMAYi6mrNTY2oLC2AriQPNSXZqM/aD6+SQ4hpOH7JsCRUwROlTqHQuXdDQ3AyPKIHIrz3UIR1686Zb0TkUKoYiCyLgYishXisSO6ZNJSeOYzG0rOALgduNbnwaShAsKHokmFJzHwrdIlCrXsEmr27wck/Cm5BMfDwD4N3YAT8gsPh5e3H0EREdoOByMIYiMhW1FZXojT3NCoLz6C+IB3ORakIrj6BaEP2BWe+Xag7rtgpBKWe3dEQ0AsuEUkIjE1BRHwSPLx8uuRnICKyFAYiC2MgInuZ+VaVfwrN5VmALhdutXnwbiyGj0EHP0V30VlwJuKBuKUukajxioHeLxauoT3h2y0RYXFJHLdERFaJgcjCGIjIEdTXVqOiJB9l2SdQm5sKp9IT8K06hUh9FnzRsnL8pbrjirWRqPaMQrNfHLTBPeAV1h0+QRHwC4mCj28Au+KIqMsxEFkYAxE5+rglXXkxirNPoCr/JJpLTkOry4J3bTZCmvMQjMp2dcVVaPxRrfVHnUsAmtyCoPcIgsY7DK5BMfAJEzPkesI/KIzBiYgshoHIwhiIiC69dEBh5jEZlhpLzsC5MhNeNdnway6Gv7ES3pr6dn+WWM27xDkEVS6hqPcIh8EnEs7+0fAK74mgmESERsZzsUoiajcGIgtjICK6wq644jxUleWhobIYTboiGGpKoKkrhUtdEbzrCxCoL7roc+HObWkqcA5HndYfRo2z3BSNMwxObmjyiYFzaCJ8opIQ0SMFfkFhXfLzEZH1YiCyMAYios7X2FCH4tzT0BVmor40B/rKHDhV58OjNh8BjXkINxbBRWNo9+eVwh957j1RF5QMt+iBCOt1FUKjEuDi6tapPwcRWQ8GIgtjICJSn765CUU5p1GecxzNdToYjXoo+mYoRj2MDTXQlJ+GZ9UZhDRmIxylF/wMo6JBucYPlc5BqHENQaNXJJSgBHhG9EZwXF+ERyewS47IjjAQWRgDEZHtrceUc2IfdGf2Q1N4BIFVxxGjz4KrRn/Jr2tQXFDmFIga5wDUuQaiyT0YRjd/wMkJGtk95wQ4OcPZJwyeET0REpuEkIg4higiK8VAZGEMRES2z2gwoKK0ABWFWagty0FDWR6MFWfhrjuDgPosRBry4XoZXXIm9YorCp0joHOPRINXFCCfLRcP/249ERbbG57efp3y8xCRZf9+a9vxeURENk+04gSFRcntYl1yeTkZqC7JQ11lEZp1hTCKwd+NOpGmoFGMgCL2BrjVFSLwl3FNYkHLeGMWUCc2uYIlcLLtWCa5oKVHJPQewVA8AuDkFQwX7yB4BkUhOCYRQaFRXG6ASGUMRERE4pehiyu6dU8GxNZOphAlFrOsLz4jW5zcqrPhW5+HEEMh/FAr12kKbq4Emo+JJ+9edLmBIudwVLp3Q6NvPJxCesI7sjdC4/siKLQbwxJRF2CXWTuwy4yIOkJXXoKSnHTo8jLQXH4WSm05nBvK4NJYAY/mSvg1lyBUKYWz5uK/hmsVd5Q5B6HKJQT17qHQe0XAJTwJob2HI6pHP45fIroEjiGyMAYiIuosTY0NKMrJQEXuSdQXZUApPQXP6kwEN+Yg3FgMp0uEpWrFA9luvVDjmwCjqzfg6gmNqxc0Lp5wC4iEX0R3hET3grdvQJf+TETWgoHIwhiIiEith/KW5J6CrjgbDWW5aK7Mh5MuG366dMQ1n4K7prldn1MBH5Q5h6Je64smFz/o3fxhdPODU0AM/OMHIabPVfDw8un0n4eoqzEQWRgDERFZGzF+KTv9IMpO7oK+9DQ0zXVw0tfLTWuog3djMYINRfBHza9+llifKdc5EqWeCWj0iYWTfxTcQ2LhG94DodE94eXj3yU/E5GlMRBZGAMREdmqal05SsTsueJMNNdUQF9bDqW+Ek51ZfCoOYtuDacQBN0lP0PMlCtx6YYarxjo/WKhDYiGe2A0fEOjERgRBx+/wC77eYguBwORhTEQEZE9Ky3MRv6JvajLTYVGlwPX2nz4NhbKFiYxU+7XiFly5U6BqNYGot4tCM3uIUBoHwT2HIaYPkPg5u7ZJT8H0bkYiCyMgYiIHJWuohTFWcdRlZeOppJTcK7Mgkd9IXyaSxBoLIOvXHzp4poUZ2Rr41Du2wfG0CR4R/VFeM9BXE6AugQDkYUxEBERXfwxKeVFOaguzUN9eQGadQVQqvLgVXEc0Q0nEYDqC35dBXxR5BKFas8o6P3i4BLcHZ4hcdC6ecLZxU2uC6V1dYO3fyh8/YMYnqhDGIgsjIGIiOjyKUYjCrIzUHh8BxpzD8O94iRC6s8g0lh4yeUELtQlV+IcgiqXUNR7RkIJT0FAz+GITbqK3XF0SQxEFsZARERkOfW11cjNONTSDVd6Bs6VZ+FVmwPf5lK4KM3QQmx6uCrN8NQ0XvRzmhQtslziUemdAL1XGJx8I+Di3w1eQd0QFNkDgWFRXLjSwVUxEFkWAxERkXrhqbTgLHSFmagvzYah9Aw8So8gpuHERbvjTBoVF5Q4BaPSNQx1nt1g8I2GNjAWXmHdERjZAx4+AXBz94CrqzuDk51iILIwBiIiIivsjss6iYJj29BccgaamgK41hfDs7EE/vpSBCvll3wkyoVam5rggiaNC5rhgmaNC/QaVzQ6eaDJ2QvNLt7Qa71h8AyBb9/x6D10LJy1fByotWMgsjAGIiIi29Lc1IiSvExU5J9GXUkm9OXZcK7KgVddHvybCxFqLIWLxtDhzxeDwjP8r4Frv9uQcNU4Ph7FSjEQWRgDERGR/bUwNTc3obGhDs2N9WhqrJd7fVOD3AzyuB7N9dXQ11fB2FAFpb4KzhUZ6KXbft76TMUIRIlrNGp84mH07QY01cKpoVI+yNe1WQeDxgUNXpFQfKKgDYyW3Xbx/a6Gu6e3avfAEVQxEFkWAxEREbVufUrfvR61h79CbOlWhKOkQ5/ToLgg3WMAGuJGI2rob9Gte7LFa3V0VQxElsVAREREF6MrL0HhmVRU5R6HvuQktDUFMLr6wOgeAI1nALRegTA2N8JQmQNtdR486gsQ1piFEFS0+Zwy+KFO44UGZy80inFLWm80uQfB6BkKJ58wuPpHwCcsDtGJg7jcQDsxEFkYAxEREVm6y+7s8b0o2r8OPrmb0asxrd1jmkzLDZT7JUMTORBe4T3hHRgO3+BI+AWGcsZcKwxEFsZAREREnf0Q3uKsE2is1aGpTgd9nQ6GukoYa0rgVFcM1/oSeDaVIVyfe8nnyxkUDao03jDAGUY4wSj2GicUu8dDn3QH+lz3R3j5+MNRVDEQWRYDERERWUvLUv7ZdBSe2IHm7P3wrkiDT3Mp/IyV8EdNu1b9Pu47Epq+ExHReyjCohLsukWpioHIshiIiIjIFgZ760oLUasrhcGgh/GXTcyWqzq6AVF53yJKKThvYHeBcyQqPGLQ5B0F+EZA6xcBj8AoeAdFQuviAsAJGo0G0GjgGxhqU0sMMBBZGAMRERHZQ+tSxqGfUb77Y0SUbEeEIR+ul7kWk0HR4Iy2B8qCr4J7r+vQffCN8uG71oqByMIYiIiIyN7om5tQlJOB0qxjqC84Aejy4FJXBPfGEvg2l8FPqYSzYoQGLTFBjEhy1zRfcJC33jRmSeOEao038vwGw6n7tYgbchOCI2OhFgYiC2MgIiIiAorzMpF9YAOMmT8jomI/opX8X/2aLKdolLtHQ+/iI5cjUNz8AA8/OHkGwtUnGO6+wfD0D4FvYDj8g8MtWi8DkYUxEBEREZ2vsrQQDXXVMBoMLWOWjHpUiselnPgRQSW70UN/Gk7tfKZcFbzg+8yvB6zO+vvNJ9MRERFRh7S06LRt1YnpNQC4bqI81pUVIfPgJjRVFsBYXwmlQQenxio4N1XBtakSHnodvAxV8DFWocrJF2o2OagaiN588025nT17Vr5OTk7GokWLcNNNN5mv2blzJ5566ins3r0bzs7OGDBgAL7//nt4eHjI8+Xl5Xj00Ufx9ddfw8nJCRMnTsSrr74Kb+//PR/myJEjmDlzJvbu3YuQkBB5/bx581T4iYmIiByHX1AYBoy5q13Xeur1UJOTmt88KioKS5Yswf79+7Fv3z7ccMMNuO2225CWlmYOQ+PHj8fYsWOxZ88eGWgeeeQRGXxMJk+eLK/fuHEj1q1bh61bt2LGjBltmsvE18fGxsrv8+KLL+KZZ57BO++8o8rPTEREROdz1qrbaWV1Y4gCAwNlaLn//vsxfPhw3HjjjXjuuecueO3x48eRlJQkg9KQIUPke+vXr8fNN9+M3NxcREZGyhYo0cJUWFgIV1dXec2TTz6JNWvW4MSJE+2qiWOIiIiIbM/l/P1WtYWoNYPBgFWrVqG2thYjRoxAcXGx7CYLDQ3FyJEjERYWhmuvvRbbtm0zf41oQfL39zeHIWHMmDGyBUl8remaUaNGmcOQMG7cOKSnp6Oiou2D9UwaGxvlTWy9ERERkf1SPRClpqbK8T5ubm546KGHsHr1atnqc+bMGXledG898MADsuVn0KBBGD16NDIyMuQ50eojAlNrWq1WtjKJc6ZrRJhqzfTadM25Fi9eLBOlaYuOju6Un52IiIisg+qBKDExEYcOHZItOg8//DCmTZuGY8eOwWg0yvMPPvgg7r33XgwcOBAvv/yyvP7999/v1JoWLFggm9dMW05OTqd+PyIiIlKX6tPuRVdWQkKCPB48eLAcDyRmiYlxPoJoLWqtT58+yM7Olsfh4eGya601vV4vZ56Jc6ZrioqK2lxjem265lyitUpsRERE5BhUbyE6l2gZEmN44uLi5KBoMdantZMnT8oZY4IYa1RZWSlnj5ls2rRJfsawYcPM14iZZ83N/1tuXMxIEy1NAQG284A6IiIistNAJLqmRFgR6xCJsUTi9ebNm+VUevFk3blz52LZsmX4/PPPcerUKSxcuFDODBMz0EytRWJavhhjJKblb9++XU7LnzRpkgxTwt133y1bocTXiOn5n3zyiWyBmjNnjpo/OhEREVkRVbvMRHfX1KlTUVBQIAcvp6SkyEUXxVR74bHHHkNDQwNmz54tu8H69+8vW3d69Ohh/oyVK1fKECQGW5sWZhQhykR87oYNG+TCjKJLLjg4WC7+2HqtIiIiInJsVrcOkTXiOkRERES2xybXISIiIiJSCwMREREROTwGIiIiInJ4DERERETk8FRfmNEWmMad85lmREREtsP0d7s988cYiNqhurpa7vlMMyIiItv8Oy5mm10Kp923g1j5Oj8/Hz4+PnLBSEunVxG0xPPSOKW/c/Fedx3e667De911eK9t716LiCPCkFisWaxVeClsIWoHcROjoqI69XuI/8H5H1jX4L3uOrzXXYf3uuvwXtvWvf61liETDqomIiIih8dARERERA6PgUhlbm5u+Nvf/ib31Ll4r7sO73XX4b3uOrzX9n2vOaiaiIiIHB5biIiIiMjhMRARERGRw2MgIiIiIofHQEREREQOj4FIRa+//jri4uLg7u6OYcOGYc+ePWqXZPMWL16Mq666Sq4qHhoaittvvx3p6eltrmloaMDMmTMRFBQEb29vTJw4EUVFRarVbC+WLFkiV3J/7LHHzO/xXltOXl4epkyZIu+lh4cH+vXrh3379pnPi/kxixYtQkREhDw/ZswYZGRkqFqzrTIYDFi4cCHi4+PlvezRoweee+65Ns/D4v3umK1bt+LWW2+VK0eL3xdr1qxpc74997W8vByTJ0+WCzb6+/vj/vvvR01NDa4UA5FKPvnkE8yZM0dOKzxw4AD69++PcePGobi4WO3SbNqWLVvkH+Bdu3Zh48aNaG5uxtixY1FbW2u+Zvbs2fj666/x2WefyevFY1nuuOMOVeu2dXv37sXbb7+NlJSUNu/zXltGRUUFrr76ari4uOC7777DsWPH8NJLLyEgIMB8zdKlS7Fs2TK89dZb2L17N7y8vOTvFBFK6fL885//xJtvvonly5fj+PHj8rW4v6+99pr5Gt7vjhG/i8XfO9EgcCHtua8iDKWlpcnf8evWrZMha8aMGbhiYto9db2hQ4cqM2fONL82GAxKZGSksnjxYlXrsjfFxcXin3TKli1b5OvKykrFxcVF+eyzz8zXHD9+XF6zc+dOFSu1XdXV1UrPnj2VjRs3Ktdee60ya9Ys+T7vteXMnz9fueaaay563mg0KuHh4cqLL75ofk/cfzc3N+W///1vF1VpPyZMmKDcd999bd674447lMmTJ8tj3m/LEL8LVq9ebX7dnvt67Ngx+XV79+41X/Pdd98pGo1GycvLu6J62EKkgqamJuzfv182BbZ+Xpp4vXPnTlVrszc6nU7uAwMD5V7cd9Fq1Pre9+7dGzExMbz3HSRa5CZMmNDmngq815azdu1aDBkyBL///e9lV/DAgQPx7rvvms9nZmaisLCwzb0Wz28SXfG815dv5MiR+PHHH3Hy5En5+vDhw9i2bRtuuukm+Zr3u3O0576KvegmE/89mIjrxd9Q0aJ0JfhwVxWUlpbKPuqwsLA274vXJ06cUK0ue2M0GuV4FtHV0LdvX/me+I/N1dVV/gd17r0X5+jyrFq1Snb5ii6zc/FeW86ZM2dkF47oZv/rX/8q7/df/vIXeX+nTZtmvp8X+p3Ce335nnzySfm0dRHgnZ2d5e/rf/zjH7KrRuD97hztua9iL/5R0JpWq5X/6L3Se89ARHbdcnH06FH5LzuyvJycHMyaNUv244uJAdS54V78i/iFF16Qr0ULkfj/bTHOQgQisqxPP/0UK1euxMcff4zk5GQcOnRI/uNKDATm/bZf7DJTQXBwsPxXx7mzbcTr8PBw1eqyJ4888ogcbPfTTz8hKirK/L64v6LLsrKyss31vPeXT3SJiUkAgwYNkv9CE5sYOC0GRIpj8a863mvLEDNukpKS2rzXp08fZGdny2PT/eTvFMuYO3eubCWaNGmSnM13zz33yAkCYharwPvdOdpzX8X+3MlHer1ezjy70nvPQKQC0cw9ePBg2Ufd+l+A4vWIESNUrc3WiXF6IgytXr0amzZtktNmWxP3XczUaX3vxbR88YeF9/7yjB49GqmpqfJfz6ZNtGKIbgXTMe+1ZYhu33OXjxDjW2JjY+Wx+P9z8ceg9b0WXT5iTAXv9eWrq6uTY1JaE/+IFb+nBd7vztGe+yr24h9Z4h9kJuJ3vfjfRow1uiJXNCSbOmzVqlVy5PyKFSvkqPkZM2Yo/v7+SmFhodql2bSHH35Y8fPzUzZv3qwUFBSYt7q6OvM1Dz30kBITE6Ns2rRJ2bdvnzJixAi50ZVrPctM4L22jD179iharVb5xz/+oWRkZCgrV65UPD09lY8++sh8zZIlS+TvkK+++ko5cuSIcttttynx8fFKfX29qrXbomnTpindunVT1q1bp2RmZipffvmlEhwcrMybN898De93x2elHjx4UG4igvz73/+Wx1lZWe2+r+PHj1cGDhyo7N69W9m2bZuc5XrXXXcpV4qBSEWvvfaa/GPh6uoqp+Hv2rVL7ZJsnvgP7ELbBx98YL5G/If15z//WQkICJB/VH73u9/J0ESWD0S815bz9ddfK3379pX/kOrdu7fyzjvvtDkvpiwvXLhQCQsLk9eMHj1aSU9PV61eW1ZVVSX//1j8fnZ3d1e6d++uPPXUU0pjY6P5Gt7vjvnpp58u+DtahND23teysjIZgLy9vRVfX1/l3nvvlUHrSmnE/7myNiYiIiIi28YxREREROTwGIiIiIjI4TEQERERkcNjICIiIiKHx0BEREREDo+BiIiIiBweAxERERE5PAYiIiIicngMRERE7bB582ZoNJrzHlZLRPaBgYiIiIgcHgMREREROTwGIiKyCUajEYsXL0Z8fDw8PDzQv39/fP755226s7755hukpKTA3d0dw4cPx9GjR9t8xhdffIHk5GS4ubkhLi4OL730UpvzjY2NmD9/PqKjo+U1CQkJ+L//+7821+zfvx9DhgyBp6cnRo4cifT0dPO5w4cP4/rrr4ePjw98fX0xePBg7Nu3r1PvCxFZBgMREdkEEYb+85//4K233kJaWhpmz56NKVOmYMuWLeZr5s6dK0PO3r17ERISgltvvRXNzc3mIPOHP/wBkyZNQmpqKp555hksXLgQK1asMH/91KlT8d///hfLli3D8ePH8fbbb8Pb27tNHU899ZT8HiLoaLVa3HfffeZzkydPRlRUlPz+4vs9+eSTcHFx6ZL7Q0RX6H8Pvicisk4NDQ2Kp6ensmPHjjbv33///cpdd92l/PTTT4r4dbZq1SrzubKyMsXDw0P55JNP5Ou7775bufHGG9t8/dy5c5WkpCR5nJ6eLj9j48aNF6zB9D1++OEH83vffPONfK++vl6+9vHxUVasWGHBn5yIugpbiIjI6p06dQp1dXW48cYbZYuNaRMtRqdPnzZfN2LECPNxYGAgEhMTZUuPIPZXX311m88VrzMyMmAwGHDo0CE4Ozvj2muvvWQtokvOJCIiQu6Li4vlfs6cOZg+fTrGjBmDJUuWtKmNiKwbAxERWb2amhq5F2OERHAxbceOHTOPI7pSYlxSe7TuAhPjlkzjmwTRDSe68yZMmIBNmzYhKSkJq1evtkh9RNS5GIiIyOqJYCEGOWdnZ8uBzq03MQDaZNeuXebjiooKnDx5En369JGvxX779u1tPle87tWrl2wZ6tevnww2rcckdYT4PDG+acOGDbjjjjvwwQcfXNHnEVHX0HbR9yEi6jAxa+uJJ56QQUOElmuuuQY6nU4GGjGbKzY2Vl737LPPIigoCGFhYXLwc3BwMG6//XZ57vHHH8dVV12F5557Dn/84x+xc+dOLF++HG+88YY8L2adTZs2TQ6SFoOqxSy2rKws2R0mBmP/mvr6ejmo+84775Qz4XJzc+Xg6okTJ3by3SEii+iy0UpERFfAaDQqr7zyipKYmKi4uLgoISEhyrhx45QtW7aYBzx//fXXSnJysuLq6qoMHTpUOXz4cJvP+Pzzz+UgavH1MTExyosvvtjmvBgcPXv2bCUiIkJ+RkJCgvL+++/Lc6bvUVFRYb7+4MGD8r3MzEylsbFRmTRpkhIdHS2/NjIyUnnkkUfMA66JyLppxP+xTLQiIlKHWIdIrP8jusn8/f3VLoeIbBDHEBEREZHDYyAiIiIih8cuMyIiInJ4bCEiIiIih8dARERERA6PgYiIiIgcHgMREREROTwGIiIiInJ4DERERETk8BiIiIiIyOExEBEREREc3f8HVIPgJNERzxEAAAAASUVORK5CYII=", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Plot the model trained for 200 total epochs loss curves\n", "pd.DataFrame(history_2.history).plot()\n", "plt.ylabel(\"loss\")\n", "plt.xlabel(\"epochs\"); # note: epochs will only show 100 since we overrid the history variable"]}, {"cell_type": "code", "execution_count": 20, "id": "5268c2a9", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import tensorflow as tf\n", "\n", "# Read in the insurance dataset\n", "insurance = pd.read_csv(\"insurance.csv\")"]}, {"cell_type": "code", "execution_count": 21, "id": "5d2f3475", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>age</th>\n", "      <th>sex</th>\n", "      <th>bmi</th>\n", "      <th>children</th>\n", "      <th>smoker</th>\n", "      <th>region</th>\n", "      <th>charges</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>19</td>\n", "      <td>female</td>\n", "      <td>27.900</td>\n", "      <td>0</td>\n", "      <td>yes</td>\n", "      <td>southwest</td>\n", "      <td>16884.92400</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>18</td>\n", "      <td>male</td>\n", "      <td>33.770</td>\n", "      <td>1</td>\n", "      <td>no</td>\n", "      <td>southeast</td>\n", "      <td>1725.55230</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>28</td>\n", "      <td>male</td>\n", "      <td>33.000</td>\n", "      <td>3</td>\n", "      <td>no</td>\n", "      <td>southeast</td>\n", "      <td>4449.46200</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>33</td>\n", "      <td>male</td>\n", "      <td>22.705</td>\n", "      <td>0</td>\n", "      <td>no</td>\n", "      <td>northwest</td>\n", "      <td>21984.47061</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>32</td>\n", "      <td>male</td>\n", "      <td>28.880</td>\n", "      <td>0</td>\n", "      <td>no</td>\n", "      <td>northwest</td>\n", "      <td>3866.85520</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   age     sex     bmi  children smoker     region      charges\n", "0   19  female  27.900         0    yes  southwest  16884.92400\n", "1   18    male  33.770         1     no  southeast   1725.55230\n", "2   28    male  33.000         3     no  southeast   4449.46200\n", "3   33    male  22.705         0     no  northwest  21984.47061\n", "4   32    male  28.880         0     no  northwest   3866.85520"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["# Check out the data\n", "insurance.head()"]}, {"cell_type": "code", "execution_count": 22, "id": "95db0bcd", "metadata": {}, "outputs": [], "source": ["from sklearn.compose import make_column_transformer\n", "from sklearn.preprocessing import MinMaxScaler, OneHotEncoder\n", "\n", "# Create column transformer (this will help us normalize/preprocess our data)\n", "ct = make_column_transformer(\n", "    (MinMaxScaler(), [\"age\", \"bmi\", \"children\"]), # get all values between 0 and 1\n", "    (OneHotEncoder(handle_unknown=\"ignore\"), [\"sex\", \"smoker\", \"region\"])\n", ")\n", "\n", "# Create X & y\n", "X = insurance.drop(\"charges\", axis=1)\n", "y = insurance[\"charges\"]\n", "\n", "# Build our train and test sets (use random state to ensure same split as before)\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)\n", "\n", "# Fit column transformer on the training data only (doing so on test data would result in data leakage)\n", "ct.fit(X_train)\n", "\n", "# Transform training and test data with normalization (MinMaxScalar) and one hot encoding (OneHotEncoder)\n", "X_train_normal = ct.transform(X_train)\n", "X_test_normal = ct.transform(X_test)"]}, {"cell_type": "code", "execution_count": 23, "id": "01238b13", "metadata": {}, "outputs": [{"data": {"text/plain": ["age                19\n", "sex            female\n", "bmi              27.9\n", "children            0\n", "smoker            yes\n", "region      southwest\n", "Name: 0, dtype: object"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["# Non-normalized and non-one-hot encoded data example\n", "X_train.loc[0]"]}, {"cell_type": "code", "execution_count": 24, "id": "4aeced6a", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([0.60869565, 0.10734463, 0.4       , 1.        , 0.        ,\n", "       1.        , 0.        , 0.        , 1.        , 0.        ,\n", "       0.        ])"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["# Normalized and one-hot encoded example\n", "X_train_normal[0]"]}, {"cell_type": "code", "execution_count": 25, "id": "906b349b", "metadata": {}, "outputs": [{"data": {"text/plain": ["((1070, 11), (1070, 6))"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "# Notice the normalized/one-hot encoded shape is larger because of the extra columns\n", "X_train_normal.shape, X_train.shape"]}, {"cell_type": "code", "execution_count": 26, "id": "2a743ecf", "metadata": {}, "outputs": [{"data": {"text/plain": ["<keras.src.callbacks.history.History at 0x284ca52a9e0>"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["# Set random seed\n", "tf.random.set_seed(42)\n", "\n", "# Build the model (3 layers, 100, 10, 1 units)\n", "insurance_model_3 = tf.keras.Sequential([\n", "  tf.keras.layers.Dense(100),\n", "  tf.keras.layers.<PERSON><PERSON>(10),\n", "  tf.keras.layers.Dense(1)\n", "])\n", "\n", "# Compile the model\n", "insurance_model_3.compile(loss=tf.keras.losses.mae,\n", "                          optimizer=tf.keras.optimizers.<PERSON>(),\n", "                          metrics=['mae'])\n", "\n", "# Fit the model for 200 epochs (same as insurance_model_2)\n", "insurance_model_3.fit(X_train_normal, y_train, epochs=200, verbose=0) "]}, {"cell_type": "code", "execution_count": 27, "id": "df52c479", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[1m9/9\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 4ms/step - loss: 3160.9170 - mae: 3160.9170  \n"]}], "source": ["insurance_model_3_loss, insurance_model_3_mae = insurance_model_3.evaluate(X_test_normal, y_test)"]}, {"cell_type": "code", "execution_count": 28, "id": "64bdf319", "metadata": {}, "outputs": [{"data": {"text/plain": ["(3405.3671875, 3160.9169921875)"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["# Compare modelling results from non-normalized data and normalized data\n", "insurance_model_2_mae, insurance_model_3_mae"]}, {"cell_type": "code", "execution_count": 30, "id": "6c9ffa0f", "metadata": {}, "outputs": [{"data": {"text/plain": ["<keras.src.callbacks.history.History at 0x284ca52a060>"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["insurance_model_3.fit(X_train_normal, y_train, epochs=200, verbose=0) "]}, {"cell_type": "code", "execution_count": 31, "id": "030413a7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[1m9/9\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 4ms/step - loss: 3159.4373 - mae: 3159.4373 \n"]}], "source": ["insurance_model_31_loss, insurance_model_31_mae = insurance_model_3.evaluate(X_test_normal, y_test)"]}, {"cell_type": "code", "execution_count": 32, "id": "c16f0360", "metadata": {}, "outputs": [{"data": {"text/plain": ["(3160.9169921875, 3159.437255859375)"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["insurance_model_3_mae, insurance_model_31_mae"]}], "metadata": {"kernelspec": {"display_name": "genai", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.7"}}, "nbformat": 4, "nbformat_minor": 5}