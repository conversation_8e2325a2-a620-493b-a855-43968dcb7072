{"cells": [{"cell_type": "code", "execution_count": 1, "id": "b06cdd95-6c78-4ad7-83a6-52ece55b9a9a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2.20.0\n"]}], "source": ["# Import TensorFlow\n", "import tensorflow as tf\n", "print(tf.__version__) # find the version number (should be 2.x+)"]}, {"cell_type": "markdown", "id": "93e5c8f0-a9d8-405b-b7dc-22c8c50bdb1a", "metadata": {}, "source": ["## tf.constant()\n", "- scalar: a single number.\n", "- vector: a number with direction (e.g. wind speed with direction).\n", "- matrix: a 2-dimensional array of numbers.\n", "- tensor: an n-dimensional arrary of numbers (where n can be any number, a 0-dimension tensor is a scalar, a 1-dimension tensor is a vector)."]}, {"cell_type": "code", "execution_count": 3, "id": "4bc1c02f-c3ab-430a-8aaf-fa9a5e3ae56f", "metadata": {}, "outputs": [{"data": {"text/plain": ["<tf.Tensor: shape=(), dtype=int32, numpy=7>"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["# Creating Tensors with tf.constant()\n", "scalar = tf.constant(7)\n", "scalar"]}, {"cell_type": "code", "execution_count": 4, "id": "b371a7e2-3e4c-4372-a5f7-2af510c5aca8", "metadata": {}, "outputs": [{"data": {"text/plain": ["0"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["# Check the number of dimensions of a tensor (ndim stands for number of dimensions)\n", "scalar.ndim"]}, {"cell_type": "code", "execution_count": 5, "id": "a80ef530-88ba-4960-8c4b-6ed882f5e406", "metadata": {}, "outputs": [{"data": {"text/plain": ["<tf.Tensor: shape=(2,), dtype=int32, numpy=array([10, 10], dtype=int32)>"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["# Create a vector (more than 0 dimensions)\n", "vector = tf.constant([10, 10])\n", "vector"]}, {"cell_type": "code", "execution_count": 5, "id": "9e465ae3-efb9-4a51-969f-d015717f2103", "metadata": {}, "outputs": [{"data": {"text/plain": ["1"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["# Check the number of dimensions of our vector tensor\n", "vector.ndim\n", "     "]}, {"cell_type": "code", "execution_count": 6, "id": "0d3f44c6-54a8-462a-85f5-61ff844a552d", "metadata": {}, "outputs": [{"data": {"text/plain": ["<tf.Tensor: shape=(2, 2), dtype=int32, numpy=\n", "array([[10,  7],\n", "       [ 7, 10]], dtype=int32)>"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["# Create a matrix (more than 1 dimension)\n", "# By default, TensorFlow creates tensors with either an int32 or float32 datatype.\n", "matrix = tf.constant([[10, 7],\n", "                      [7, 10]])\n", "matrix"]}, {"cell_type": "code", "execution_count": 7, "id": "24659ef0-d4dc-475e-8e51-46f3580e9e77", "metadata": {}, "outputs": [{"data": {"text/plain": ["2"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["matrix.ndim"]}, {"cell_type": "code", "execution_count": 8, "id": "63380e53-2fdf-497e-8717-666982d0dacd", "metadata": {}, "outputs": [{"data": {"text/plain": ["<tf.Tensor: shape=(3, 2), dtype=float16, numpy=\n", "array([[10.,  7.],\n", "       [ 3.,  2.],\n", "       [ 8.,  9.]], dtype=float16)>"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["# Create another matrix and define the datatype\n", "matrix1 = tf.constant([[10., 7.],\n", "                              [3., 2.],\n", "                              [8., 9.]], dtype=tf.float16) # specify the datatype with 'dtype'\n", "matrix1\n", "     "]}, {"cell_type": "code", "execution_count": 9, "id": "13995682-fae5-48e0-8a6a-dbd4a778b6bd", "metadata": {}, "outputs": [{"data": {"text/plain": ["<tf.Tensor: shape=(3, 2, 3), dtype=int32, numpy=\n", "array([[[ 1,  2,  3],\n", "        [ 4,  5,  6]],\n", "\n", "       [[ 7,  8,  9],\n", "        [10, 11, 12]],\n", "\n", "       [[13, 14, 15],\n", "        [16, 17, 18]]], dtype=int32)>"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["# How about a tensor? (more than 2 dimensions, although, all of the above items are also technically tensors)\n", "tensor = tf.constant([[[1, 2, 3],\n", "                       [4, 5, 6]],\n", "                      [[7, 8, 9],\n", "                       [10, 11, 12]],\n", "                      [[13, 14, 15],\n", "                       [16, 17, 18]]])\n", "tensor"]}, {"cell_type": "code", "execution_count": 10, "id": "a34955cc-dc3a-4b3c-9429-886a982f77c5", "metadata": {}, "outputs": [{"data": {"text/plain": ["3"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["tensor.ndim"]}, {"cell_type": "markdown", "id": "fcfd0270-55cc-4b86-8db9-58a5ea0522d3", "metadata": {}, "source": ["## For example, you might turn a series of images into tensors with shape (224, 224, 3, 32), where:\n", "\n", "- 224, 224 (the first 2 dimensions) are the height and width of the images in pixels.\n", "- 3 is the number of colour channels of the image (red, green blue).\n", "- 32 is the batch size (the number of images a neural network sees at any one time)."]}, {"cell_type": "markdown", "id": "23a4f302-4098-4c78-bd87-a7d075bba873", "metadata": {}, "source": ["## tf.Variable()\n", "\n", "You can also (although you likely rarely will, because often, when working with data, tensors are created for you automatically) create tensors using tf.Variable().\n", "\n", "The difference between tf.Variable() and tf.constant() is tensors created with tf.constant() are immutable (can't be changed, can only be used to create a new tensor), where as, tensors created with tf.Variable() are mutable (can be changed)."]}, {"cell_type": "code", "execution_count": 2, "id": "03280825-15ac-4c51-a436-aada1147adaa", "metadata": {}, "outputs": [{"data": {"text/plain": ["(<tf.Variable 'Variable:0' shape=(2,) dtype=int32, numpy=array([10,  7], dtype=int32)>,\n", " <tf.Tensor: shape=(2,), dtype=int32, numpy=array([10,  7], dtype=int32)>)"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["# Create the same tensor with tf.Variable() and tf.constant()\n", "changeable_tensor = tf.Variable([10, 7])\n", "unchangeable_tensor = tf.constant([10, 7])\n", "changeable_tensor, unchangeable_tensor"]}, {"cell_type": "code", "execution_count": 12, "id": "73d7583e-abb6-4814-9aeb-652aeead51ad", "metadata": {}, "outputs": [{"ename": "TypeError", "evalue": "'ResourceVariable' object does not support item assignment", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mType<PERSON>rror\u001b[39m                                 <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[12]\u001b[39m\u001b[32m, line 2\u001b[39m\n\u001b[32m      1\u001b[39m \u001b[38;5;66;03m# Will error (requires the .assign() method)\u001b[39;00m\n\u001b[32m----> \u001b[39m\u001b[32m2\u001b[39m \u001b[43mchangeable_tensor\u001b[49m\u001b[43m[\u001b[49m\u001b[32;43m0\u001b[39;49m\u001b[43m]\u001b[49m = \u001b[32m7\u001b[39m\n\u001b[32m      3\u001b[39m changeable_tensor\n", "\u001b[31mTypeError\u001b[39m: 'ResourceVariable' object does not support item assignment"]}], "source": ["# Will error (requires the .assign() method)\n", "changeable_tensor[0] = 7\n", "changeable_tensor"]}, {"cell_type": "code", "execution_count": 13, "id": "57794fb4-c435-4fe9-af2c-51478aa2cda2", "metadata": {}, "outputs": [{"data": {"text/plain": ["<tf.Variable 'Variable:0' shape=(2,) dtype=int32, numpy=array([7, 7], dtype=int32)>"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["# Won't error\n", "changeable_tensor[0].assign(7)\n", "changeable_tensor"]}, {"cell_type": "code", "execution_count": 14, "id": "a809bf09-acd8-4346-bb1f-6e19187023fe", "metadata": {}, "outputs": [{"ename": "AttributeError", "evalue": "'tensorflow.python.framework.ops.EagerTensor' object has no attribute 'assign'", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mAttributeError\u001b[39m                            <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[14]\u001b[39m\u001b[32m, line 2\u001b[39m\n\u001b[32m      1\u001b[39m \u001b[38;5;66;03m# Will error (can't change tf.constant())\u001b[39;00m\n\u001b[32m----> \u001b[39m\u001b[32m2\u001b[39m \u001b[43munchangeable_tensor\u001b[49m\u001b[43m[\u001b[49m\u001b[32;43m0\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m.\u001b[49m\u001b[43massign\u001b[49m(\u001b[32m7\u001b[39m)\n\u001b[32m      3\u001b[39m unchangleable_tensor\n", "\u001b[36mFile \u001b[39m\u001b[32mC:\\Demo\\genai\\Lib\\site-packages\\tensorflow\\python\\framework\\tensor.py:260\u001b[39m, in \u001b[36mTensor.__getattr__\u001b[39m\u001b[34m(self, name)\u001b[39m\n\u001b[32m    252\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m name \u001b[38;5;129;01min\u001b[39;00m {\u001b[33m\"\u001b[39m\u001b[33mT\u001b[39m\u001b[33m\"\u001b[39m, \u001b[33m\"\u001b[39m\u001b[33mastype\u001b[39m\u001b[33m\"\u001b[39m, \u001b[33m\"\u001b[39m\u001b[33mravel\u001b[39m\u001b[33m\"\u001b[39m, \u001b[33m\"\u001b[39m\u001b[33mtranspose\u001b[39m\u001b[33m\"\u001b[39m, \u001b[33m\"\u001b[39m\u001b[33mreshape\u001b[39m\u001b[33m\"\u001b[39m, \u001b[33m\"\u001b[39m\u001b[33mclip\u001b[39m\u001b[33m\"\u001b[39m, \u001b[33m\"\u001b[39m\u001b[33msize\u001b[39m\u001b[33m\"\u001b[39m,\n\u001b[32m    253\u001b[39m             \u001b[33m\"\u001b[39m\u001b[33mtolist\u001b[39m\u001b[33m\"\u001b[39m, \u001b[33m\"\u001b[39m\u001b[33mdata\u001b[39m\u001b[33m\"\u001b[39m}:\n\u001b[32m    254\u001b[39m   \u001b[38;5;66;03m# TODO(wangpeng): Export the enable_numpy_behavior knob\u001b[39;00m\n\u001b[32m    255\u001b[39m   \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mAttributeError\u001b[39;00m(\n\u001b[32m    256\u001b[39m       \u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mtype\u001b[39m(\u001b[38;5;28mself\u001b[39m).\u001b[34m__name__\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m object has no attribute \u001b[39m\u001b[33m'\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mname\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m'\u001b[39m\u001b[33m. \u001b[39m\u001b[33m\"\u001b[39m + \u001b[33m\"\"\"\u001b[39m\n\u001b[32m    257\u001b[39m \u001b[33m    If you are looking for numpy-related methods, please run the following:\u001b[39m\n\u001b[32m    258\u001b[39m \u001b[33m    tf.experimental.numpy.experimental_enable_numpy_behavior()\u001b[39m\n\u001b[32m    259\u001b[39m \u001b[33m  \u001b[39m\u001b[33m\"\"\"\u001b[39m)\n\u001b[32m--> \u001b[39m\u001b[32m260\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[34;43m__getattribute__\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mname\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[31mAttributeError\u001b[39m: 'tensorflow.python.framework.ops.EagerTensor' object has no attribute 'assign'"]}], "source": ["# Will error (can't change tf.constant())\n", "unchangeable_tensor[0].assign(7)\n", "unchangleable_tensor"]}, {"cell_type": "markdown", "id": "7038b0cc-95b1-48da-a8db-b3a2d61c422e", "metadata": {}, "source": ["Which one should you use? tf.constant() or tf.Variable()?\n", "\n", "It will depend on what your problem requires. However, most of the time, TensorFlow will automatically choose for you (when loading data or modelling data)."]}, {"cell_type": "markdown", "id": "3baa914c-9b74-406f-80db-a6b00156c1c7", "metadata": {}, "source": ["## Creating random tensors"]}, {"cell_type": "code", "execution_count": null, "id": "dbe06b96-91a9-4fa0-9ed3-8d4f6660cda5", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "genai", "language": "python", "name": "genai"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.7"}}, "nbformat": 4, "nbformat_minor": 5}