{"cells": [{"cell_type": "code", "execution_count": 2, "id": "19eb4d8c-4619-4ccb-9983-9af06a9d9726", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2.20.0\n", "Notebook last run (end-to-end): 2025-10-09 10:11:59.849813\n"]}], "source": ["import tensorflow as tf\n", "print(tf.__version__) # check the version (should be 2.x+)\n", "\n", "import datetime\n", "print(f\"Notebook last run (end-to-end): {datetime.datetime.now()}\")"]}, {"cell_type": "code", "execution_count": 3, "id": "7afe731a-4e39-482a-8a64-5b28cea6b600", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "# Create features\n", "X = np.array([-7.0, -4.0, -1.0, 2.0, 5.0, 8.0, 11.0, 14.0])\n", "\n", "# Create labels\n", "y = np.array([3.0, 6.0, 9.0, 12.0, 15.0, 18.0, 21.0, 24.0])\n", "\n", "# Visualize it\n", "plt.scatter(X, y);"]}, {"cell_type": "code", "execution_count": 4, "id": "fe012f34-91f0-4a10-af1e-8f327302bfab", "metadata": {}, "outputs": [{"data": {"text/plain": ["(<tf.Tensor: shape=(3,), dtype=string, numpy=array([b'bedroom', b'bathroom', b'garage'], dtype=object)>,\n", " <tf.Tensor: shape=(1,), dtype=int32, numpy=array([939700], dtype=int32)>)"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["# Example input and output shapes of a regression model\n", "house_info = tf.constant([\"bedroom\", \"bathroom\", \"garage\"])\n", "house_price = tf.constant([939700])\n", "house_info, house_price"]}, {"cell_type": "code", "execution_count": 6, "id": "183d892f-4383-4dce-8a85-b2d2e0b4cfbb", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "# Create features (using tensors)\n", "X = tf.constant([-7.0, -4.0, -1.0, 2.0, 5.0, 8.0, 11.0, 14.0])\n", "\n", "# Create labels (using tensors)\n", "y = tf.constant([3.0, 6.0, 9.0, 12.0, 15.0, 18.0, 21.0, 24.0])\n", "\n", "# Visualize it\n", "plt.scatter(X, y);"]}, {"cell_type": "code", "execution_count": 7, "id": "7f4cd0f1-fc74-4316-93b3-b27850df6094", "metadata": {}, "outputs": [{"data": {"text/plain": ["<PERSON><PERSON><PERSON><PERSON><PERSON>([3])"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["house_info.shape"]}, {"cell_type": "code", "execution_count": 8, "id": "1285b167-7de6-4de0-81a1-0543927fef3f", "metadata": {}, "outputs": [{"data": {"text/plain": ["Tensor<PERSON><PERSON><PERSON>([8])"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["X.shape"]}, {"cell_type": "code", "execution_count": 9, "id": "cc1d7ac9-9d00-4e89-8658-e8cded261751", "metadata": {}, "outputs": [{"data": {"text/plain": ["(<PERSON><PERSON><PERSON><PERSON><PERSON>([]), <PERSON><PERSON><PERSON><PERSON><PERSON>([]))"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["# Take a single example of X\n", "input_shape = X[0].shape \n", "\n", "# Take a single example of y\n", "output_shape = y[0].shape\n", "\n", "input_shape, output_shape # these are both scalars (no shape)"]}, {"cell_type": "code", "execution_count": 10, "id": "3cdbb219-f0ab-4f16-a95f-547de9b545d5", "metadata": {}, "outputs": [{"data": {"text/plain": ["(<tf.Tensor: shape=(), dtype=float32, numpy=-7.0>,\n", " <tf.Tensor: shape=(), dtype=float32, numpy=3.0>)"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["# Let's take a look at the single examples invidually\n", "X[0], y[0]"]}, {"cell_type": "code", "execution_count": 11, "id": "638da880-cdf6-46ec-8988-28487bcc701d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Epoch 1/5\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 411ms/step - loss: 20.4947 - mae: 20.4947\n", "Epoch 2/5\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 43ms/step - loss: 20.2134 - mae: 20.2134\n", "Epoch 3/5\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 57ms/step - loss: 19.9322 - mae: 19.9322\n", "Epoch 4/5\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 43ms/step - loss: 19.6509 - mae: 19.6509\n", "Epoch 5/5\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 46ms/step - loss: 19.3697 - mae: 19.3697\n"]}, {"data": {"text/plain": ["<keras.src.callbacks.history.History at 0x146fe105010>"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["# Set random seed\n", "tf.random.set_seed(42)\n", "\n", "# Create a model using the Sequential API\n", "model = tf.keras.Sequential([\n", "  tf.keras.layers.Dense(1)\n", "])\n", "\n", "# Compile the model\n", "model.compile(loss=tf.keras.losses.mae, # mae is short for mean absolute error\n", "              optimizer=tf.keras.optimizers.SGD(), # SGD is short for stochastic gradient descent\n", "              metrics=[\"mae\"])\n", "\n", "# Fit the model\n", "# model.fit(X, y, epochs=5) # this will break with TensorFlow 2.7.0+\n", "model.fit(tf.expand_dims(X, axis=-1), y, epochs=5)"]}, {"cell_type": "code", "execution_count": 12, "id": "e874d399-c127-4564-ac93-96507b05f912", "metadata": {}, "outputs": [{"data": {"text/plain": ["(<tf.Tensor: shape=(8,), dtype=float32, numpy=array([-7., -4., -1.,  2.,  5.,  8., 11., 14.], dtype=float32)>,\n", " <tf.Tensor: shape=(8,), dtype=float32, numpy=array([ 3.,  6.,  9., 12., 15., 18., 21., 24.], dtype=float32)>)"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "# Check out X and y\n", "X, y"]}, {"cell_type": "code", "execution_count": 13, "id": "65f1e693-aad6-429a-b05c-f8317ec54c58", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 56ms/step\n"]}, {"data": {"text/plain": ["array([[-20.577967]], dtype=float32)"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["# Make a prediction with the model\n", "x = np.array([[17.0]])\n", "model.predict(x)"]}, {"cell_type": "markdown", "id": "5a27737f-e17c-48d2-9571-d93a2b9b2db2", "metadata": {}, "source": ["Improving a model\n", "- Creating a model - here you might want to add more layers, increase the number of hidden units (also called neurons) within each layer, change the activation functions of each layer.\n", "- Compiling a model - you might want to choose optimization function or perhaps change the learning rate of the optimization function.\n", "- Fitting a model - perhaps you could fit a model for more epochs (leave it training for longer) or on more data (give the model more examples to learn from)."]}, {"cell_type": "code", "execution_count": 19, "id": "385dd65e-745c-4afb-bb68-7cd2ccb19414", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Epoch 1/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 304ms/step - loss: 11.0779 - mae: 11.0779\n", "Epoch 2/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 51ms/step - loss: 10.9454 - mae: 10.9454\n", "Epoch 3/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 51ms/step - loss: 10.8129 - mae: 10.8129\n", "Epoch 4/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 45ms/step - loss: 10.6804 - mae: 10.6804\n", "Epoch 5/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 46ms/step - loss: 10.5479 - mae: 10.5479\n", "Epoch 6/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 48ms/step - loss: 10.4154 - mae: 10.4154\n", "Epoch 7/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 48ms/step - loss: 10.2829 - mae: 10.2829\n", "Epoch 8/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 48ms/step - loss: 10.1504 - mae: 10.1504\n", "Epoch 9/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 53ms/step - loss: 10.0179 - mae: 10.0179\n", "Epoch 10/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 47ms/step - loss: 9.8854 - mae: 9.8854\n", "Epoch 11/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 42ms/step - loss: 9.7529 - mae: 9.7529\n", "Epoch 12/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 41ms/step - loss: 9.6204 - mae: 9.6204\n", "Epoch 13/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 39ms/step - loss: 9.4879 - mae: 9.4879\n", "Epoch 14/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 44ms/step - loss: 9.3554 - mae: 9.3554\n", "Epoch 15/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 46ms/step - loss: 9.2229 - mae: 9.2229\n", "Epoch 16/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 40ms/step - loss: 9.0904 - mae: 9.0904\n", "Epoch 17/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 40ms/step - loss: 8.9579 - mae: 8.9579\n", "Epoch 18/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 40ms/step - loss: 8.8254 - mae: 8.8254\n", "Epoch 19/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 40ms/step - loss: 8.6929 - mae: 8.6929\n", "Epoch 20/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 46ms/step - loss: 8.5604 - mae: 8.5604\n", "Epoch 21/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 46ms/step - loss: 8.4279 - mae: 8.4279\n", "Epoch 22/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 48ms/step - loss: 8.2954 - mae: 8.2954\n", "Epoch 23/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 42ms/step - loss: 8.1629 - mae: 8.1629\n", "Epoch 24/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 40ms/step - loss: 8.0304 - mae: 8.0304\n", "Epoch 25/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 41ms/step - loss: 7.8979 - mae: 7.8979\n", "Epoch 26/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 51ms/step - loss: 7.7654 - mae: 7.7654\n", "Epoch 27/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 85ms/step - loss: 7.6329 - mae: 7.6329\n", "Epoch 28/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 48ms/step - loss: 7.5004 - mae: 7.5004\n", "Epoch 29/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 41ms/step - loss: 7.3679 - mae: 7.3679\n", "Epoch 30/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 40ms/step - loss: 7.2825 - mae: 7.2825\n", "Epoch 31/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 48ms/step - loss: 7.2769 - mae: 7.2769\n", "Epoch 32/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 47ms/step - loss: 7.2713 - mae: 7.2713\n", "Epoch 33/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 83ms/step - loss: 7.2656 - mae: 7.2656\n", "Epoch 34/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 42ms/step - loss: 7.2600 - mae: 7.2600\n", "Epoch 35/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 43ms/step - loss: 7.2544 - mae: 7.2544\n", "Epoch 36/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 44ms/step - loss: 7.2488 - mae: 7.2488\n", "Epoch 37/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 43ms/step - loss: 7.2431 - mae: 7.2431\n", "Epoch 38/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 41ms/step - loss: 7.2375 - mae: 7.2375\n", "Epoch 39/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 41ms/step - loss: 7.2319 - mae: 7.2319\n", "Epoch 40/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 41ms/step - loss: 7.2262 - mae: 7.2262\n", "Epoch 41/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 49ms/step - loss: 7.2206 - mae: 7.2206\n", "Epoch 42/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 61ms/step - loss: 7.2150 - mae: 7.2150\n", "Epoch 43/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 46ms/step - loss: 7.2094 - mae: 7.2094\n", "Epoch 44/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 47ms/step - loss: 7.2038 - mae: 7.2038\n", "Epoch 45/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 44ms/step - loss: 7.1981 - mae: 7.1981\n", "Epoch 46/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 42ms/step - loss: 7.1925 - mae: 7.1925\n", "Epoch 47/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 48ms/step - loss: 7.1869 - mae: 7.1869\n", "Epoch 48/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 43ms/step - loss: 7.1813 - mae: 7.1813\n", "Epoch 49/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 42ms/step - loss: 7.1756 - mae: 7.1756\n", "Epoch 50/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 86ms/step - loss: 7.1700 - mae: 7.1700\n", "Epoch 51/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 45ms/step - loss: 7.1644 - mae: 7.1644\n", "Epoch 52/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 50ms/step - loss: 7.1587 - mae: 7.1587\n", "Epoch 53/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 39ms/step - loss: 7.1531 - mae: 7.1531\n", "Epoch 54/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 44ms/step - loss: 7.1475 - mae: 7.1475\n", "Epoch 55/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 41ms/step - loss: 7.1419 - mae: 7.1419\n", "Epoch 56/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 41ms/step - loss: 7.1363 - mae: 7.1363\n", "Epoch 57/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 48ms/step - loss: 7.1306 - mae: 7.1306\n", "Epoch 58/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 44ms/step - loss: 7.1250 - mae: 7.1250\n", "Epoch 59/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 42ms/step - loss: 7.1194 - mae: 7.1194\n", "Epoch 60/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 42ms/step - loss: 7.1137 - mae: 7.1137\n", "Epoch 61/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 44ms/step - loss: 7.1081 - mae: 7.1081\n", "Epoch 62/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 87ms/step - loss: 7.1025 - mae: 7.1025\n", "Epoch 63/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 54ms/step - loss: 7.0969 - mae: 7.0969\n", "Epoch 64/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 56ms/step - loss: 7.0913 - mae: 7.0913\n", "Epoch 65/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 44ms/step - loss: 7.0856 - mae: 7.0856\n", "Epoch 66/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 41ms/step - loss: 7.0800 - mae: 7.0800\n", "Epoch 67/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 39ms/step - loss: 7.0744 - mae: 7.0744\n", "Epoch 68/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 47ms/step - loss: 7.0688 - mae: 7.0688\n", "Epoch 69/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 44ms/step - loss: 7.0631 - mae: 7.0631\n", "Epoch 70/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 46ms/step - loss: 7.0575 - mae: 7.0575\n", "Epoch 71/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 42ms/step - loss: 7.0519 - mae: 7.0519\n", "Epoch 72/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 43ms/step - loss: 7.0463 - mae: 7.0463\n", "Epoch 73/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 43ms/step - loss: 7.0406 - mae: 7.0406\n", "Epoch 74/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 91ms/step - loss: 7.0350 - mae: 7.0350\n", "Epoch 75/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 42ms/step - loss: 7.0294 - mae: 7.0294\n", "Epoch 76/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 45ms/step - loss: 7.0238 - mae: 7.0238\n", "Epoch 77/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 47ms/step - loss: 7.0181 - mae: 7.0181\n", "Epoch 78/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 48ms/step - loss: 7.0125 - mae: 7.0125\n", "Epoch 79/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 50ms/step - loss: 7.0069 - mae: 7.0069\n", "Epoch 80/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 44ms/step - loss: 7.0013 - mae: 7.0013\n", "Epoch 81/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 47ms/step - loss: 6.9956 - mae: 6.9956\n", "Epoch 82/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 45ms/step - loss: 6.9900 - mae: 6.9900\n", "Epoch 83/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 62ms/step - loss: 6.9844 - mae: 6.9844\n", "Epoch 84/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 73ms/step - loss: 6.9788 - mae: 6.9788\n", "Epoch 85/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 45ms/step - loss: 6.9731 - mae: 6.9731\n", "Epoch 86/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 41ms/step - loss: 6.9675 - mae: 6.9675\n", "Epoch 87/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 40ms/step - loss: 6.9619 - mae: 6.9619\n", "Epoch 88/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 73ms/step - loss: 6.9563 - mae: 6.9563\n", "Epoch 89/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 51ms/step - loss: 6.9506 - mae: 6.9506\n", "Epoch 90/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 46ms/step - loss: 6.9450 - mae: 6.9450\n", "Epoch 91/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 41ms/step - loss: 6.9394 - mae: 6.9394\n", "Epoch 92/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 39ms/step - loss: 6.9338 - mae: 6.9338\n", "Epoch 93/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 48ms/step - loss: 6.9281 - mae: 6.9281\n", "Epoch 94/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 52ms/step - loss: 6.9225 - mae: 6.9225\n", "Epoch 95/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 53ms/step - loss: 6.9169 - mae: 6.9169\n", "Epoch 96/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 48ms/step - loss: 6.9113 - mae: 6.9113\n", "Epoch 97/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 82ms/step - loss: 6.9056 - mae: 6.9056\n", "Epoch 98/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 39ms/step - loss: 6.9000 - mae: 6.9000\n", "Epoch 99/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 52ms/step - loss: 6.8944 - mae: 6.8944\n", "Epoch 100/100\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 46ms/step - loss: 6.8888 - mae: 6.8888\n"]}, {"data": {"text/plain": ["<keras.src.callbacks.history.History at 0x146fde71a70>"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["# Set random seed\n", "tf.random.set_seed(42)\n", "\n", "# Create a model (same as above)\n", "model = tf.keras.Sequential([\n", "  tf.keras.layers.Dense(1)\n", "])\n", "\n", "# Compile model (same as above)\n", "model.compile(loss=tf.keras.losses.mae,\n", "              optimizer=tf.keras.optimizers.SGD(),\n", "              metrics=[\"mae\"])\n", "\n", "# Fit model (this time we'll train for longer)\n", "model.fit(tf.expand_dims(X, axis=-1), y, epochs=100) # train for 100 epochs not 10"]}, {"cell_type": "code", "execution_count": 20, "id": "8e513429-552e-4ce4-ad8e-b52502527ef5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 49ms/step\n"]}, {"data": {"text/plain": ["array([[29.84189]], dtype=float32)"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["# Make a prediction with the model\n", "x = np.array([[17.0]])\n", "model.predict(x)"]}, {"cell_type": "code", "execution_count": 22, "id": "a1063eea-923d-4ca0-b40c-e350fb130aa0", "metadata": {}, "outputs": [{"data": {"text/plain": ["50"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["# Make a bigger dataset\n", "X = np.arange(-100, 100, 4)\n", "y = np.arange(-90, 110, 4)\n", "len(X)\n", "len(y)"]}, {"cell_type": "code", "execution_count": 23, "id": "bb8ffafd", "metadata": {}, "outputs": [{"data": {"text/plain": ["(40, 10)"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["# Split data into train and test sets\n", "X_train = X[:40] # first 40 examples (80% of data)\n", "y_train = y[:40]\n", "\n", "X_test = X[40:] # last 10 examples (20% of data)\n", "y_test = y[40:]\n", "\n", "len(X_train), len(X_test)"]}, {"cell_type": "code", "execution_count": 24, "id": "bb23d5c4", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x700 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure(figsize=(10, 7))\n", "# Plot training data in blue\n", "plt.scatter(X_train, y_train, c='b', label='Training data')\n", "# Plot test data in green\n", "plt.scatter(X_test, y_test, c='g', label='Testing data')\n", "# Show the legend\n", "plt.legend();"]}, {"cell_type": "code", "execution_count": 29, "id": "5d1f01d5", "metadata": {}, "outputs": [{"data": {"text/plain": ["<keras.src.callbacks.history.History at 0x146821997f0>"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["# Set random seed\n", "tf.random.set_seed(42)\n", "\n", "# Create a model (same as above)\n", "model = tf.keras.Sequential([\n", "  tf.keras.layers.Dense(1, input_shape=[1]) # define the input_shape to our model\n", "])\n", "\n", "# Compile model (same as above)\n", "model.compile(loss=tf.keras.losses.mae,\n", "              optimizer=tf.keras.optimizers.SGD(),\n", "              metrics=[\"mae\"])\n", "\n", "# Fit the model to the training data\n", "model.fit(X_train, y_train, epochs=100, verbose=0) # verbose controls how much gets output"]}, {"cell_type": "code", "execution_count": 26, "id": "261ad738", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">Model: \"sequential_5\"</span>\n", "</pre>\n"], "text/plain": ["\u001b[1mModel: \"sequential_5\"\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━┓\n", "┃<span style=\"font-weight: bold\"> Layer (type)                         </span>┃<span style=\"font-weight: bold\"> Output Shape                </span>┃<span style=\"font-weight: bold\">         Param # </span>┃\n", "┡━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━┩\n", "│ dense_5 (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Dense</span>)                      │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">1</span>)                   │               <span style=\"color: #00af00; text-decoration-color: #00af00\">2</span> │\n", "└──────────────────────────────────────┴─────────────────────────────┴─────────────────┘\n", "</pre>\n"], "text/plain": ["┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━┓\n", "┃\u001b[1m \u001b[0m\u001b[1mLayer (type)                        \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mOutput Shape               \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1m        Param #\u001b[0m\u001b[1m \u001b[0m┃\n", "┡━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━┩\n", "│ dense_5 (\u001b[38;5;33mDense\u001b[0m)                      │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m1\u001b[0m)                   │               \u001b[38;5;34m2\u001b[0m │\n", "└──────────────────────────────────────┴─────────────────────────────┴─────────────────┘\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\"> Total params: </span><span style=\"color: #00af00; text-decoration-color: #00af00\">4</span> (20.00 B)\n", "</pre>\n"], "text/plain": ["\u001b[1m Total params: \u001b[0m\u001b[38;5;34m4\u001b[0m (20.00 B)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\"> Trainable params: </span><span style=\"color: #00af00; text-decoration-color: #00af00\">2</span> (8.00 B)\n", "</pre>\n"], "text/plain": ["\u001b[1m Trainable params: \u001b[0m\u001b[38;5;34m2\u001b[0m (8.00 B)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\"> Non-trainable params: </span><span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> (0.00 B)\n", "</pre>\n"], "text/plain": ["\u001b[1m Non-trainable params: \u001b[0m\u001b[38;5;34m0\u001b[0m (0.00 B)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\"> Optimizer params: </span><span style=\"color: #00af00; text-decoration-color: #00af00\">2</span> (12.00 B)\n", "</pre>\n"], "text/plain": ["\u001b[1m Optimizer params: \u001b[0m\u001b[38;5;34m2\u001b[0m (12.00 B)\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["model.summary()"]}, {"cell_type": "code", "execution_count": 30, "id": "8e164a2b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["WARNING:tensorflow:6 out of the last 7 calls to <function TensorFlowTrainer.make_predict_function.<locals>.one_step_on_data_distributed at 0x0000014682558EA0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has reduce_retracing=True option that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.\n", "\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 55ms/step\n"]}], "source": ["# Make predictions\n", "y_preds = model.predict(X_test)"]}, {"cell_type": "code", "execution_count": 31, "id": "e21003fb", "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAA0EAAAJGCAYAAACdj47VAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjYsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvq6yFwwAAAAlwSFlzAAAPYQAAD2EBqD+naQAAXMFJREFUeJzt3QmcW3W5//FnulKkU6B0YwgdFllkX7SCLOkFacGrYRnxT9mKCIIsDdQr9F6VxaXIZioXBQUKV4ELjAVxoVigRRCKCFZEC1JsIQwFFKHDIm1p8399f+HkJmcyk2Qmyzk5n/frFYfzO7+kZ9LjNM88v9/ztGQymYwBAAAAQEQMavQFAAAAAEA9EQQBAAAAiBSCIAAAAACRQhAEAAAAIFIIggAAAABECkEQAAAAgEghCAIAAAAQKUMs5NavX28vv/yyjRw50lpaWhp9OQAAAAAaRC1Q33rrLdt8881t0KBBzRsEKQCKxWKNvgwAAAAAAZFOp22LLbZo3iBIGSDvG21tbW305QAAAABokO7ubpcg8WKEpg2CvCVwCoAIggAAAAC0lNgmQ2EEAAAAAJFCEAQAAAAgUgiCAAAAAERK6PcElWvdunW2du3aRl8GAmzo0KE2ePDgRl8GAAAAamxIFGqFv/LKK/bmm282+lIQAhtvvLGNHz+enlMAAABNrOmDIC8AGjt2rG244YZ8uEWvwfK7775rr732mjueMGFCoy8JAAAANTKk2ZfAeQHQ6NGjG305CLgRI0a4rwqEdM+wNA4AAKA5NXVhBG8PkDJAQDm8e4X9YwAAAM2rqYMgD0vgUC7uFQAAgOYXiSAIAAAAADwEQRHR3t5uqVSq7PmLFi1yWZFGVNW78cYbXZU2AAAAoBYIggJGgUdfjwsvvLBfr/v444/bqaeeWvb8fffd11auXGmjRo2yZgzyAAAAEF1NXR2uWtatM3voIbOVK1U62Wz//c1qVThMgYfntttus69//ev27LPP5sY22mijgrLOqoA3ZEjpv8YxY8ZUdB3Dhg1z/XIAAACAZkMmqIR585RlMJs82WzatOxXHWu8FhR4eA9lYZT98Y6feeYZGzlypN1zzz2211572fDhw+3hhx+2559/3hKJhI0bN84FSR/96Eftvvvu6zNTote97rrr7IgjjnAV0T784Q/b3Xff3etyOG+J2r333ms77rij+3OmTp1aELS9//77dvbZZ7t5Kkl+3nnn2YknnmiHH354n9+zXnvLLbd016Href311wvOl/r+4vG4vfDCC3bOOefkMmai1znmmGOsra3NvfYuu+xit956a7//bgAAANAcCIL6oECno8PspZcKx7u6suO1CoRKOf/88+2SSy6xpUuX2q677mpvv/22HXbYYXb//ffbH/7wBxecfPrTn7YXX3yxz9e56KKL7Oijj7annnrKPf/YY4+1f/7zn73OVzPRyy+/3H784x/bb37zG/f6X/7yl3Pnv/Od79jNN99sc+fOtd/+9rfW3d1td911V5/X8Nhjj9nJJ59sZ555pi1ZssQmT55s3/zmNwvmlPr+5s2bZ1tssYVdfPHFLijzArP33nvPBYu//OUv7emnn3bLAY8//nj73e9+V9b7DAAAgCaVCblVq1Zl9G3oq9+//vWvzF/+8hf3tVLvv5/JbLFFJqN3qNijpSWTicWy82pl7ty5mVGjRuWOFy5c6L7Xu+66q+Rzd9ppp8xVV12VO544cWLmu9/9bu5Yr/PVr341d/z222+7sXvuuafgz3rjjTdy16LjZcuW5Z5z9dVXZ8aNG5c71n9fdtllueP3338/s+WWW2YSiUSv13nMMcdkDjvssIKxz33ucwXfd3++v9586lOfysycObPX8wO5ZwAAABDc2CAfmaBeaA+QPwOUT2FEOp2dV2977713j0yJMjJapqalaFoypixRqUyQskieD33oQ9ba2mqvvfZar/O1pGybbbbJHU+YMCE3f9WqVfbqq6/axz72sdz5wYMHu0xMX3SdkyZNKhjbZ599qvL9ab/UN77xDbcMbtNNN3XP03K+Us8DAABAc6MwQi/ytrpUZV41KWDJpwBhwYIFbqnatttuayNGjLCOjg5bs2ZNn68zdOjQgmPtpVm/fn1F87NJpdrq7/d32WWX2Zw5c9xeKAVCet+SyWTJ5wEAAKA869avs4defMhWvrXSJoycYPtvub8NHlSjCmJVRBDUC1WBq+a8WtL+m+nTp7uiAl7mZMWKFXW9BhVxUOECleI+4IADcpmYJ5980nbfffden6fsjvYF5Vu8eHHF35+q2enP8z9PBRWOO+44d6wA769//at95CMfGeB3CwAAgHlL59mM+TPspe7/Wz61ResWNmfqHDtyxyMtyFgO1wuVwd5iC2U7ip/XeCyWnddoquym4gAqLPDHP/7Rpk2b1mdGp1bOOussmz17tv3sZz9zZb1nzJhhb7zxRq5aWzGqJjd//nyX5Xnuuefsv//7v91xpd+fqt+pWENXV5f94x//yD1PGaRHHnnELZ/74he/6JbsAQAAYOABUMftHQUBkHR1d7lxnQ8ygqBeqA/QnDnZ//Z/hveOVXG6Vv2CKnHllVfaJpts4hqcqmralClTbM8996z7dagktkpSn3DCCW5fj/bg6Fo22GCDXp/z8Y9/3H70ox+5ZWu77bab/frXv7avfvWrFX9/qgyn7JD2LHk9kfQ6mqf5KqOtMuOlynUDAACg9BI4ZYAy1nNbhDeWnJ9084KqRdURLMRUhllLsbQxXxv786lE8vLly22rrbbq84N4X1QGe8aMwiIJygApADoy2Fm+hlO2RsvdVIZbBQrCoBr3DAAAQDNbtGKRTb5pcsl5C09caPH2uAUlNsjHnqASFOgkEtkqcCqCoD1AWgIXhAxQ0KhhqTI5Bx54oK1evdotbVNAoeVrAAAAaA4r31pZ1XmNQBBUBgU88foGsaE0aNAgu/HGG101NyUYd955Z7vvvvtcNggAAADNYcLICVWd1wgEQaiaWCzmKrIBAACgee2/5f6uCpyKIBTbF9RiLe685gUVhREAAAAAlE19gFQG2wt48nnHqampQPcLIggCAAAAUBH1Aeo8utPaWtsKxpUB0njQ+wSxHA4AAACIsHXr19lDLz7kChloH4+WsZWTxVGgk9g+0a/nNhpBEAAAABBR85bOcz1/8pueKpuj5W7lZHMU8NS7DHY1sBwOAAAAiGgA1HF7R0EAJCp4oHGdb1YEQQAAAEAEl8DNmD+jaHU3byw5P+nmNSOCoIi78MILbffdd2/Inz19+nQ7/PDDG/JnAwAARNlDLz7UIwPkD4TS3Wk3rxkRBAVMS0tLnw8FLQN57bvuuqtgTI1N77//fguDFStWuO9hyZIljb4UAACAUFv51sqqzotMEPSb3/zGPv3pT9vmm29e9MN1JpOxr3/96zZhwgQbMWKEHXzwwfbcc88VzPnnP/9pxx57rLW2ttrGG29sJ598sr399tsWNEoDLlqxyG79063uay3TgitXrsw9UqmUe2/yxxS0VNNGG21ko0ePruprAgAAINgmjJxQ1XmRCYLeeecd22233ezqq68uev7SSy+1733ve3bNNdfYY489Zh/60IdsypQp9t577+XmKAD685//bAsWLLBf/OIXLrA69dRTLUi0Iax9TrtNvmmyTZs3zX3Vca02io0fPz73GDVqlAsw88f+93//13bccUfbYIMNbIcddrDvf//7ueeuWbPGzjzzTBd46vzEiRNt9uzZ7lx7e7v7esQRR7jX9I79y+G8JWqXX365ex0FSGeccYatXbs2N0fB2Kc+9SkX3G611VZ2yy23uNdT0NabdevW2bnnnuuCXb3mV77yFRco55s/f77tt99+uTn//u//bs8//3zuvP4s2WOPPdz3EI9nK5E8/vjj9slPftI222wz954deOCB9uSTTw747wIAAKBZ7b/l/q4KnL/ZqUfjsdaYm9eM+h0EHXroofbNb37Tfaj204dbfSD+6le/aolEwnbddVf7n//5H3v55ZdzGaOlS5e6D73XXXedTZo0yX34veqqq9yHfM0LgqBVzLj55ptddu1b3/qWe/++/e1v29e+9jW76aab3HkFnXfffbfdfvvt9uyzz7r5XrCjQEHmzp3rghjvuJiFCxe64ENf9do33nije3hOOOEE93e0aNEi++lPf2o//OEP7bXXXuvz2q+44gr3GjfccIM9/PDDLgt455139gisFSj9/ve/d0v0Bg0a5O6v9evXu/O/+93v3Nf77rvPfQ/z5mXf/7feestOPPFE97qLFy+2D3/4w3bYYYe5cQAAABQvbT1n6hz33/5AyDtOTU2FoudPYPoELV++3F555RW3BM6j39Ar2Hn00Uft//2//+e+6jf+e++9d26O5uuDrzJHxYIrWb16tXt4uru7G1IxQzeHKmaoQVS9bo4LLrjABRNHHnlkLjPyl7/8xa699loXBLz44osuAFBAqUyJMkGeMWPGuK96z5VR6ssmm2xi//3f/22DBw922SZlfRSUnHLKKfbMM8+4IERBlPd3p0BWf25fFBTPmjUrd+3KEN57770Fc4466qiCYwVMum59jzvvvHPue1CWKP97+Ld/+7eC5yko0/f54IMPumwSAAAAejpyxyOt8+jOon2CFACV0ycorGoSBCkAknHjxhWM69g7p69jx44tvJghQ2zTTTfNzSlGy7suuuiiWlx2vytm1KNBlLIkys5o35SCEc/777/vAkxvKZuWhW2//fY2depUFwAccsghFf9ZO+20kwuAPFoW96c//cn9tzJM+nvac889c+e33XZbFzj1ZtWqVS5zoyDYo9dQEJW/JE57xpTpUhD8j3/8I5cBUnCnIKg3r776qss6KjOljJSW3r377rvueQAAAFGhX+Lrs6mKGWgvj5aylfpl/ZE7Hul+qV/p88KuJkFQLSmboCVT+ZmgWCzW9BUzvIIRP/rRjwqCCfECFgUmysLdc889Lltz9NFHu+xaZ2dnRX/W0KFDC46VVfICklpSoQ1lr/Q9quCG/kwFP9rr1BdlwV5//XWbM2eOe/7w4cNtn332Kfk8AACAZqFtGsUyOlryViqjM3jQ4Lr8Ur/pS2R7S5X0G/p8OvbO6at/H4myGtor0tdyLX3AVcW0/EcUKmYoi6bA4G9/+5vLvOQ/vIIBovfjc5/7nAskbrvtNrdnR++pF9woSzIQyjLp7+kPf/hDbmzZsmX2xhtv9PocZaqUTVKGx6PXeOKJJ3LHCmKUZVJG56CDDnLFH/yvOWzYMPfV/z389re/tbPPPtvtA1IWS/eIMkkAAABRELR97JENgvShXIFMfv8ZZWz0IVi/oRd9ffPNNws+CD/wwAPut//+TEcjBLFihpYBajmgCiD89a9/dUvUVOjgyiuvdOf19dZbb3X7dnT+jjvucH8P2h8jKpKgvxMtN+wraOmL9ggpu6QqfipUoGBI/61KccoY9WbGjBl2ySWXuMIYur4vfelL7u/fo+V02uuj/TwKqnQv5Gf8RMsn9eeooIYCai2zE+1H+vGPf+yKRegeU9VBzQMAAGh2pfaxi/ax17LFS6SCIC3PUtNKr3GllmHpv7UPQx+Gk8mkqx6namX6sK6KYspkqPyy6Df92rei/S36MK3f5qu8s4omaF6jBbFixhe+8AVXhECBzy677OJKQavimpcJGjlypCtNrr02H/3oR11z0V/96leu2ISoqILKkWv5oMpM95cq/SkzdcABB7gCFvo71J+tsty9mTlzph1//PFu6ZoCYM3PL36ha1RlQAXFWgJ3zjnn2GWXXVbwGtpHpABQhSB0j6jyoFx//fUuqNNyQP0Zygr595sBAAA0o0r2seP/tGT8zVrKpE3okydP7jGuD7n6YK6XVTUz/WZfv/FXxTL1tNluu+1yc7VMS4HPz3/+c/chWNXB9CFXDTzLpQyTllspK+BfGqeeRArOFCT09QG90vWVygA1e8WMSrz00ksusNI+JC1lC7Nq3DMAAAD1cuufbnW9LEu55chb7JhdjrFm191HbFCVICgoah0E9bfSRjPTUjVlApWNUtU3NT7t6upyS/D8RRXChiAIAACEyaIVi2zyTT0TE34LT1wYieIH3WUGQaGrDtcIUayY0Ze1a9faf/7nf7oiDVrWtu+++7rGrGEPgAAAAMLG28euIgjF9gVpG4fO13MfexgQBKFiU6ZMcQ8AAAAEYx+7qsAp4MkPhBq1jz2y1eEAAAAA1If2qXce3WltrW0F48oAaZx97D2RCQIAAAACor970RXoJLZPsI+9TARBAAAAQAAUq0qsbI6Wu5WTzWEfe/lYDgcAAAAEIADSvh5/zx8VPNC4zqN6CIIAAACABi+BUwaoWHU3byw5P+nmoToIggAAAIAG0j4efwbIHwilu9NuHqqDICjipk+fbocffnjuOB6PWzKZHNBrVuM1AAAAokKFDKo5D6URBAU4OGlpaXGPYcOG2bbbbmsXX3yxvf/++zX9c+fNm2ff+MY3ypq7aNEid31vvvlmv18DAAAg6lTJrZrzUBrV4cqxbp3ZQw+ZrVxpNmGC2f77mw2ufbnBqVOn2ty5c2316tX2q1/9ys444wwbOnSozZo1q2DemjVrXKBUDZtuumkgXgMAACAqVMpaVeBUBKHYviA1PdV5zUN1kAkqZd48s/Z2s8mTzaZNy37VscZrbPjw4TZ+/HibOHGinX766XbwwQfb3XffnVvC9q1vfcs233xz23777d38dDptRx99tG288cYuEEkkErZixYrc661bt87OPfdcd3706NH2la98xTKZTJ9L2RSAnXfeeRaLxdz1KCN1/fXXu9edrPfCzDbZZBOXEdJ1FXuNN954w0444QQ3b8MNN7RDDz3Unnvuudz5G2+80V3TvffeazvuuKNttNFGLgBcqaAzL+v0sY99zD70oQ+5uZ/4xCfshRdeqMn7DgAAUE8qba0y2F7Ak887Tk1NBbPnz7p1+qBmduut2a86DgGCoL4o0OnoMHvJt1Gtqys7XodAKN+IESNc1kfuv/9+e/bZZ23BggX2i1/8wtauXWtTpkyxkSNH2kMPPWS//e1vc8GE95wrrrjCBRw33HCDPfzww/bPf/7T7rzzzj7/TAUvt956q33ve9+zpUuX2rXXXuteV0HRT3/6UzdH16GAZc6c7P95/RQc/f73v3cB3KOPPuoCr8MOO8xds+fdd9+1yy+/3H784x/bb37zG3vxxRfty1/+sjunJYAK+g488EB76qmn3GuceuqpLvACAABoBuoD1Hl0p7W1thWMKwOk8XL6BEUpWTBQLIfrjaLYGTPMfJkSR2P6AK5sRyJR86VxChoU9ChTctZZZ9nf//53lxG57rrrcsvgfvKTn9j69evdmBccaCmdsibKohxyyCGWSqXcUrojj8z+n+iaa65xr9mbv/71r3b77be7QEtZKNl66617LHsbO3as+3OKUcZHwY+Csn333deN3XzzzS6Iuuuuu+yzn/2sG1NApOvZZptt3PGZZ57p9kBJd3e3rVq1yv793/89d14ZIwAAgKBSOWtVc1MxA+3l0VK2UpkcBTqJ7RMVP6+hyYJMpniyoLPT7IPPnEFEENQb7QHyZ4Dy6S88nc7Oi9emM68yPMq6KEBQgDNt2jS78MIL3d6gXXbZpWAf0B//+EdbtmyZywTle++99+z55593QYSyNZMmTcqdGzJkiO299949lsR5lixZYoMHD3YZmP5S9kh/Tv6fq6V4WsKncx4tk/MCHJkwYYK99tpruWBL2SRluj75yU+6gEzL/jQHAAAgaNTYVH1/8steK6OjJW+lMjoKeOLttfls2YzJgv5iOVxv8vajVGVeP2jPjQIRZVP+9a9/2U033eQyQOJ99bz99tu21157ufn5D2VzFDz1d/ldvajgQz5ls/KDM2W1tAxO2aTbbrvNtttuO1u8eHHdrg8AAKDcAKjj9o4efX9U9EDjOh+pZEFAEQT1ptwsQw2zEQp0VIhgyy23dNmUvuy5554uWNLSND0n/zFq1Cj3UObkscceyz1He22eeOKJXl9T2SZloB588MGi571MlAou9EbL1vTn5P+5r7/+uttH9JGPfMQqsccee7jlfI888ojtvPPOdsstt1T0fAAAgFovgVMGqFiFN28sOT/p5oXaysYnCwaKIKg3KoO9xRbZdF4xGo/FsvMC4Nhjj7XNNtvMVYRTYYTly5e7vUBnn322vfRBpD5jxgy75JJL3F6cZ555xr70pS/16PGTr7293U488UT7/Oc/757jvab2CYmq1iljo2V72qekbJTfhz/8YXdNp5xyiivGoGV7xx13nLW1tbnxcujPVfCjTJAqwv361792AR/7ggAAQJBoL48/A+QPhNLdaTcv1CY0PlkwUARBvdH6Ra/amT8Q8o5TqcCsc9SeGlVVU9ZIhQ8UIJx88sluT1Bra6ubM3PmTDv++ONdYLPPPvu4/UNHHHFEn6/7gx/8wDo6OlzAtMMOO7hg5p133nHnFMhcdNFFdv7559u4ceNcMYNitJRNS/VU2EB/rpa5qe+RfwlcX9+bgrajjjrKLYNTZTjti/riF79Y8fsEAABQKypmUM15gbV/uJIFxbRketsVHxKqHKalXtr4733Y9ygAUBZhq622sg022KD/lS+08St/3aP+UhUABbjiBfqnKvcMAACIpEUrFtnkm7J9FPuy8MSFwS9+UG51OMkPJ7zAqEHV4fqKDfKRCSpFf3lqOLpwoZn2oOjr8uUEQAAAACigctaqAudveOrReKw15uaFvuHpkUdmA522wr5GLkMU8PLYQonscmjJW43KYAMAAKA5qLy1ymCrCpwCnvwCCV5glJqaClbfn3lFVj0pkNG2kFKBjM5rj7eqwKkIgvYAaQlcQLaL9IVMEAAAAFAl6gPUeXSntbUWZkiUIdJ4qT5BDVnS9tJLxRue6ny5yYJjjsl+DUEAJGSCAAAAgF6onLWquamYwYSRE9xStlKZHAU6ie0TFT+vrtaFv+HpQEQiCAp57QfUEfcKAADwqLGp+v7kl71WRkdL3kpldBTwBLr4wUMVNDxtwm0hTb0czivB/O677zb6UhAS3r1SbvluAADQvAGQ9vb4+/50dXe5cZ0PtZXhb3g6EE2dCRo8eLBtvPHG9tprr+X6zai5J1AsA6QASPeK7hndOwAAILpL4JQByi9s4NGYihwk5yfdkrdALXGLWMPTgWjqIEjGjx/vvnqBENAXBUDePQMAAKJJe3n8GSB/IJTuTrt5gV7yVk7D066u4vuClDjQ+QA3PB2Ipg+ClPmZMGGCjR071tauXdvoy0GAaQkcGSAAAKBiBtWcF0iDB2fLYKsKnAKeYg1PU6mmLIoQiSDIow+3fMAFAABAKarmVs15gXXkBw1Pi/UJUgAU8IanAxGZIAgAAAAoh8pZqwqciiAU2xekPUE6r3mBorLXlTYuPTK8DU8HgiAIAAAAyKNiByqDrSpwCnjyAyEdS2pqKlhFEdTYtFhGR0veSmV0Bn/Q8DRCmrpENgAAANAf6gPUeXSntbW2FYwrA6TxUn2C6h4AaW+Pv++Pih5oXOdRoCUT8u6Q3d3dNmrUKFu1apW1trY2+nIAAAAQwJLXquSmQgbax6NlbOVmcQby3LotgWtv773xqVflbfnypl/iVklswHI4AAAANC01NVXPn/yS18rmaLlbOdkcBTyBLoOtvTy9BUCifEc6nZ0XsSVvfWE5HAAAAJo2ANK+Hn/PHxU80LjOh56KGVRzXkQQBAEAAKDpaBmbMkDFqrt5Y8n5STcv1FTNrZrzIoIgCAAAAE1H+3j8GSB/IJTuTrt5oaZy1trz4zU49dN4LJadhxyCIAAAADQdFTKo5rzAUrEDlcEWfyDkHavxaQSKIlSCIAgAAABNR5XcqjmvrtXeFi0yu/XW7Fcdl6I+QJ2dZm2F5bxdhkjjpfoERRDV4QAAANB0VMpaVeBUBKHYviA1PdV5zWuKhqc6n0hkq8CpCIL2AGkJHBmgosgEAQAAoOmotLXKYHsBTz7vODU1FZyeP9VoeKqAR2Wwjzkm+5UAqFcEQQAAAGhK6gPUeXSntbUWLhNTBkjj5fQJqgsteVMGSD19/LyxZLK8pXEoS0smU+zdbr6usAAAAAg3lbNWNTcVM9BeHi1lKyeT09/n1Y32/kyeXHrewoU0PK1SbMCeIAAAAASeGpuq709+2WtldLTkrVRGRwFPvD3AwQMNT+uO5XAAAAAIfADUcXtHj74/KnqgcZ0PNRqe1h1BEAAAAAJLS9mUASpW4c0bS85PunmhRcPTuiMIAgAAQGBpL48/A+QPhNLdaTcvtGh4WncEQQAAAAgsFTOo5rzANj2l4WldURgBAAAAgaVqbtWcF+impzQ8rRtKZAMAACCwtNenfU67K4JQbF+QGp+qStzyGcuDUfbaa3rq/4jtLWsjqxOI2KCmy+Ha29utpaWlx+OMM85w5+PxeI9zp512Wi0vCQAAACGiwEZlsL2AJ593nJqaCkYARNPT0KhpEPT444/bypUrc48FCxa48c9+9rO5OaecckrBnEsvvbSWlwQAAICQUR+gzqM7ra21cL+MMkAaL9UnqG60jC1/CVyxQCidzs5D8+4JGjNmTMHxJZdcYttss40deOCBubENN9zQxo8fX8vLAAAAQMCWuKmam4oZaC/P/lvuXzKTo0AnsX2i4ufVFU1PQ6NuhRHWrFljP/nJT+zcc891y948N998sxtXIPTpT3/avva1r7nAqDerV692j/x1fwAAAAgHNTZV35/8stfK6GjJW6mMjgKeeHvcAoump6FRtxLZd911l7355ps2ffr03Ni0adNcALRw4UKbNWuW/fjHP7bjjjuuz9eZPXu22+zkPWJqHAUAAIBQBEAdt3f06Pujogca1/lQo+lpaNStOtyUKVNs2LBh9vOf/7zXOQ888IAddNBBtmzZMrdsrtxMkAIhqsMBAAAEv8pbb41PA1flbaDV4ST/YzbV4aJTHc7zwgsv2H333Wdf+MIX+pw3adIk91VBUG+GDx/uvqH8BwAAAIJNe3l6C4BE5a/T3Wk3L9RoehoKddkTNHfuXBs7dqx96lOf6nPekiVL3NcJrJMEAABoKipmUM15daNy1pU2L6XpaeDVPAhav369C4JOPPFEGzLk//64559/3m655RY77LDDbPTo0fbUU0/ZOeecYwcccIDtuuuutb4sAAAA1JGquVVzXt2WtqnvT37Za2V05swpndFRwBMPcBGHiKv5cjgtg3vxxRft85//fMG49gfp3CGHHGI77LCDzZw504466qg+9wwBAAAgnFTOWnt+/A1PPRqPtcbcvEDt7fH3/enqyo7rPEKrboURGr35CQAAAMGoDuftAfJ4gVFgGp9qCVx7e++NT1XkQBmh5ctZ4hYwgSqMAAAAACjAUaDT1lpYNEAZosAEQKK9PL0FQKIcQjqdnYdQqluzVAAAADRXyWtVclMhA+3j0TK2ckpbK9BJbJ/o13PrRsUMqjkPgUMQBAAAgIqXtc2YP6Og5LWyOXOmzikrm6OAJ94e4KIB5VYqpqJxaLEcDgAAABXv6/H3/Onq7nLjOh96KmetPT9eg1M/jcdi2XkIJYIgAAAAlL0EThmg/KIGHm8sOT/p5oWaih2oDLb4AyHvOJWiKEKIEQQBAACgLNrH488A+QOhdHfazQsUVXtbtMjs1luzX3VcivoAdXaatRUWcXAZIo2X6hOEQGNPEAAAAMqiQgbVnBf4hqc6n0hkq8CpCIL2AGkJHBmg0CMIAgAAQFlUya2a8+rW8NTfFtNreFpORkcBTzzARRzQLyyHAwAAQFlUylpV4Lzmpn4aj7XG3LyG05I3ZYD8AZB4Y8lkeUvj0HQIggAAAFAWlbZWGWzxB0LecWpqKhg9f2h4ij4QBAEAAKBs6gPUeXSntbUWFgxQhkjj5fQJqgsanqIP7AkCAACIMK0Gq3TfvwKdxPYJVwVORRC0B0hL4AKRAfLQ8BR9IAgCAACIqIEUTlPAE2+PB7/hqYogFNsXpH4/Ok/D00hiORwAAEAEeYXT/NtmvMJpOh9qNDxFHwiCAAAAIia0hdMqbXpKw1P0guVwAAAAEVNJ4bTAtMjp79o9Gp6iCIIgAACAiAld4bSBNj2l4Sl8WA4HAAAQMaEqnBbatXsIMoIgAACAiPEKp/nrBXg0HosFpHAaTU9RAwRBAAAAEROqwmmhW7uHMCAIAgAAiKDQFE4L1do9hEVLJlNsgWV4dHd326hRo2zVqlXW2tra6MsBAACoO22H6W/xs4E8ty50ge3tpZueLl8esAtHkGMDqsMBAACEWH8rR4emcJq3dk9V4BTw5AdCgVu7h7BgORwAAEBIeZWj/XUDvMrROh9IND1Fg7EcDgAAIIS8VWK9FU4L7CqxgaSuAr92D43GcjgAAIAmVknl6MAsd6PpKQKC5XAAAAAhFLrK0TQ9RYAQBAEAAIRQ6CpH0/QUAUIQBAAAEELaDqOtNP5mpx6Nx2LZeYEQutQVmhlBEAAAQAh5laPFHwgFsnJ06FJXaGYEQQAAACEVqsrRoUtdoZlRHQ4AACAg+lMBWoFOIhGCytE0PUWAEAQBAACEvH1OaCpHe6mrYt+oAqBApa7QzGiWCgAAEND2OV6CJHBL2wbauJSmp2hwbEAQBAAA0ECKB9rbe68erUBIiZLlywMUJwwkbQUEIDagMAIAAEADha59jpe28l90V1d2XOeBgCMIAgAAaKBQtc9R2koZoGILibyxZDI7DwgwgiAAAIAGClX7nNClrYDiCIIAAAAaKFTtc0KVtgJ6RxAEAAAQgPY54g+EAtc+J1RpK6B3BEEAAAABaZ/T1lY4rgxRoMpjhyptBfSOZqkAAABV1p82OAp0EomAt8/x0laqAqeAJ79AQuDSVkDvCIIAAAAC0kJHsUM8buFIWxX7JhUABSZtBfSOZqkAAABVbqHj/3TlJUkCtbStEekuICCxAUEQAABAlWKC9vbeK0grEFKyZPlyYgWg0bEBhREAAACqgBY6QHgQBAEAAFQBLXSA8CAIAgAAqAJa6ADhQRAEAABQBbTQAcKDIAgAAKCKLXTEHwjRQgcIFoIgAACAKrfQaWsrHFeGqKnKYwMhR7NUAACAKrbBUaCTSNBCB4hsJujCCy+0lpaWgscOO+yQO//ee+/ZGWecYaNHj7aNNtrIjjrqKHv11VdreUkAAABlNT1Vz5/Jk82mTct+1bHGy6GAJx43O+aY7FcCICBiy+F22mknW7lyZe7x8MMP586dc8459vOf/9zuuOMOe/DBB+3ll1+2I8kTAwCABlKg09HRs+dPV1d2vNxACECEl8MNGTLExo8f32NcXVyvv/56u+WWW+zf/u3f3NjcuXNtxx13tMWLF9vHP/7xWl8aAABAjyVwM2ZkG5v6aUwFDpLJ7HI3sjtAeNU8E/Tcc8/Z5ptvbltvvbUde+yx9uKLL7rxJ554wtauXWsHH3xwbq6Wym255Zb26KOP9vp6q1evtu7u7oIHAABANWgfjz8D5A+E0unsPADhVdMgaNKkSXbjjTfa/Pnz7Qc/+IEtX77c9t9/f3vrrbfslVdesWHDhtnGG29c8Jxx48a5c72ZPXu2jRo1KveIqeA+AABAFaiQQTXnAYjgcrhDDz0099+77rqrC4omTpxot99+u40YMaJfrzlr1iw799xzc8fKBBEIAQCAalAlt2rOAxBMde0TpKzPdtttZ8uWLXP7hNasWWNvvvlmwRxVhyu2h8gzfPhwa21tLXgAAABUg0pZq6ePv9mpR+P63avmAQivugZBb7/9tj3//PM2YcIE22uvvWzo0KF2//33584/++yzbs/QPvvsU8/LAgAAcFTsYM6c7H/7AyHvOJWiKAIQdjUNgr785S+70tcrVqywRx55xI444ggbPHiwHXPMMW4/z8knn+yWti1cuNAVSjjppJNcAERlOAAA0Cjq1tHZadbWVjiuDJHG6eYBhF9N9wS99NJLLuB5/fXXbcyYMbbffvu58tf6b/nud79rgwYNck1SVfVtypQp9v3vf7+WlwQAACJY9lrV3FTMQHt5tJStVCZHgY7KYFf6PADh0JLJFKuEHx4qjKCskvoOsT8IAADkU2NT9f3JL3utjI6WvJHRAZpPubFBXfcEAQAA1DMA6ujo2fenqys7rvMAookgCAAANOUSOGWAiq138caSyew8ANFDEAQAAJqO9vL4M0D+QCidzs4DED0EQQAAoOmomEE15wFoLgRBAACg6aiaWzXnAWguBEEAAKDpqJy1qsD5G556NB6LZecBiB6CIAAA0HTUz0dlsMUfCHnHqRR9f4CoIggCAABNSX2AOjvN2toKx5Uh0jh9goDoGtLoCwAAACiHylmrmpuKGWgvj5aylcrkKNBJJCp/HoDmRhAEAAACT41N1fcnv+y1Mjpa8lYqo6OAJx6v+SUCCBGWwwEAgMAHQB0dPfv+dHVlx3UeACpBEAQAAAK9BE4ZIDU39fPGksnsPAAoF0EQAAAILO3l8WeA/IFQOp2dBwDlIggCAACBpWIG1ZwHAEIQBAAAAkvV3Ko5DwCEIAgAAASWylmrCpy/4alH47FYdh4AlIsgCAAABJbKW6sMtvgDIe84laLvD4DKEAQBAIBAUx+gzk6ztrbCcWWINF6qTxAA+NEsFQAA1I1KWauSmwoZaB+PlrGVk8VRoJNI9O+5AOBHEAQAAOpCTU3V8ye/5LWyOVruVk42RwFPPF7TSwQQESyHAwAAdQmAOjp69vzp6sqO6zwA1AtBEAAAqPkSOGWA1NjUzxtLJrPzAKAeCIIAAEBNaR+PPwPkD4TS6ew8AKgHgiAAAFBTKmRQzXkAMFAEQQAAoKZUya2a8wBgoAiCAABATamUtarA+ZudejQei2XnAUA9EAQBAICaUmlrlcEWfyDkHadS9PwBUD8EQQAAoObUB6iz06ytrXBcGSKNl9MnCACqhWapAACgYipnrWpuKmagvTxaylYqk6NAJ5Go/HkAUG0EQQAAoCJqbKq+P/llr5XR0ZK3UhkdBTzxeM0vEQD6xHI4AABQUQDU0dGz709XV3Zc5wEg6AiCAABA2UvglAFSc1M/byyZzM4DgCAjCAIAAGXRXh5/BsgfCKXT2XkAEGQEQQAAoCwqZlDNeQDQKARBAACgLKrmVs15ANAoBEEAAKAsKmetKnD+hqcejcdi2XkAEGQEQQAAoCwqb60y2OIPhLzjVIq+PwCCjyAIAACUTX2AOjvN2toKx5Uh0nipPkEAEAQ0SwUAIKJUylqV3FTIQPt4tIytnCyOAp1Eon/PBYAgIAgCACCC1NRUPX/yS14rm6PlbuVkcxTwxOM1vUQAqBmWwwEAEMEAqKOjZ8+frq7suM4DQDMjCAIAIGJL4JQBUmNTP28smczOA4BmRRAEAECEaB+PPwPkD4TS6ew8AGhWBEEAAESIChlUcx4AhBFBEAAAEaJKbtWcBwBhRBAEAECEqJS1qsD5m516NB6LZecBQLMiCAIAIEJU2lplsMUfCHnHqRQ9fwA0N4IgAAAiRn2AOjvN2toKx5Uh0ng5fYIAIMxolgoAQMipnLWquamYgfbyaClbqUyOAp1EovLnAUAzIAgCACDE1NhUfX/yy14ro6Mlb6UyOgp44vGaXyIABA7L4QAACHEA1NHRs+9PV1d2XOcBAHUOgmbPnm0f/ehHbeTIkTZ27Fg7/PDD7dlnny2YE4/HraWlpeBx2mmn1fKyAABoiiVwygCpuamfN5ZMZucBAOoYBD344IN2xhln2OLFi23BggW2du1aO+SQQ+ydd94pmHfKKafYypUrc49LL720lpcFAEDoaS+PPwPkD4TS6ew8AEAd9wTNnz+/4PjGG290GaEnnnjCDjjggNz4hhtuaOPHj6/lpQAA0FRUzKCa8wAgSuq6J2jVqlXu66abblowfvPNN9tmm21mO++8s82aNcvefffdXl9j9erV1t3dXfAAACBqVM2tmvMAIErqVh1u/fr1lkwm7ROf+IQLdjzTpk2ziRMn2uabb25PPfWUnXfeeW7f0LxednNqn9FFF11Ur8sGACCQVM5aVeBUBKHYviA1PtV5zQMAFGrJZIr96Ky+008/3e655x57+OGHbQv9VO7FAw88YAcddJAtW7bMttlmm6KZID08ygTFYjGXZWptba3Z9QMAENTqcJL/r7kCIKHxKYCo6e7utlGjRpWMDeqyHO7MM8+0X/ziF7Zw4cI+AyCZNGmS+6ogqJjhw4e7byj/AQBAFCnAUaDT1lY4rn9qCYAAoEHL4ZRkOuuss+zOO++0RYsW2VZbbVXyOUuWLHFfJ7CIGQAQMSpnrWpuKmagfwa1lE0NTfuiQCeRqPx5ABBlNQ2CVB77lltusZ/97GeuV9Arr7zixpWiGjFihD3//PPu/GGHHWajR492e4LOOeccVzlu1113reWlAQAQuKVt6vuTX/ZaGZ05c0pndBTwxOM1v0QAaBo13ROkxqfFzJ0716ZPn27pdNqOO+44e/rpp13vIO3tOeKII+yrX/1q2cvcyl33BwBA0Pf2+P9FZm8PAFSm3NigboURaoUgCAAQ9iVw7e29Nz71qrwtX84SNwAIVWEEAABQnPby9BYAiX5VmU5n5wEAqoMgCACABlIxg2rOAwCURhAEAEADlVsMlaKpAFA9BEEAADSQyllrz08vtYTceCyWnQcAqA6CIAAAGkjFDlQGW/yBkHecSlEUAQCqiSAIAIAGU/lrlcFuayscV4aI8tgAELJmqQAARLHktSq5qZCB9vFoGVs5WRwFOolE/54LAKgMQRAAAFVsejpjRmHJa2VztNytnGyOAp54vKaXCABgORwAANULgDo6evb86erKjus8ACAYCIIAAKjCEjhlgNTY1M8bSyaz8wAAjUcQBADAAGkfjz8D5A+E0unsPABA4xEEAQAwQCpkUM15AIDaIggCAGCAVMmtmvMAALVFEAQAwACplLWqwPmbnXo0Hotl5wEAGo8gCACAAVJpa5XBFn8g5B2nUvT8AYCgIAgCAKAK1Aeos9Osra1wXBkijZfTJwgAUB80SwUAoAiVs1Y1NxUz0F4eLWUrlclRoJNIVP48AEB9EQQBAOCjxqbq+5Nf9loZHS15K5XRUcATj9f8EgEAA8ByOAAAfAFQR0fPvj9dXdlxnQcAhBtBEAAAeUvglAFSc1M/byyZzM4DAIQXQRAAAB/QXh5/BsgfCKXT2XkAgPAiCAIA4AMqZlDNeQCAYCIIAgDgA6rmVs15AIBgIggCAOADKmetKnD+hqcejcdi2XkAgPAiCAIAIK+8tcpgiz8Q8o5TKfr+AEDYEQQBAJBHfYA6O83a2grHlSHSeKk+QQCA4KNZKgCgqamctaq5qZiB9vJoKVupTI4CnUSi8ucBAMKBIAgA0LTU2FR9f/LLXiujoyVvpTI6Cnji8ZpfIgCgAVgOBwBo2gCoo6Nn35+uruy4zgMAookgCADQlEvglAFSc1M/byyZzM4DAEQPQRAAoOloL48/A+QPhNLp7DwAQPQQBAEAmo6KGVRzHgCguRAEAQCajqq5VXMeAKC5EAQBAJqOylmrCpy/4alH47FYdh4AIHoIggAATUflrVUGW/yBkHecStH3BwCiiiAIANCU1Aeos9Osra1wXBkijZfqEwQAaF40SwUABJ5KWauSmwoZaB+PlrGVk8VRoJNI9O+5AIDmRRAEAAg0NTVVz5/8ktfK5mi5WznZHAU88XhNLxEAEDIshwMABDoA6ujo2fOnqys7rvMAAFSKIAgAENglcMoAqbGpnzeWTGbnAQBQCYIgAEAgaR+PPwPkD4TS6ew8AAAqQRAEAAgkFTKo5jwAADwEQQCAQFIlt2rOAwDAQxAEAAgklbJWFTh/s1OPxmOx7DwAACpBEAQACCSVtlYZbPEHQt5xKkXPHwBA5QiCAACBpT5AnZ1mbW2F48oQabycPkEAAPjRLBUAUDcqZ61qbipmoL08WspWKpOjQCeRqPx5AAD0hiAIAFAXamyqvj/5Za+V0dGSt1IZHQU88XjNLxEAEBEshwMA1CUA6ujo2fenqys7rvMAANQLQRAAoOZL4JQBUnNTP28smczOAwCgHgiCAAA1pb08/gyQPxBKp7PzAACoB4IgAEBNqZhBNecBANAUQdDVV19t7e3ttsEGG9ikSZPsd7/7XaMvCQBQJarmVs15AACEPgi67bbb7Nxzz7ULLrjAnnzySdttt91sypQp9tprrzX60gAAVaBy1qoC52946tF4LJadBwBAJIKgK6+80k455RQ76aST7CMf+Yhdc801tuGGG9oNN9zQ6EsDAFSBylurDLb4AyHvOJWi7w8AICJB0Jo1a+yJJ56wgw8++P8uaNAgd/zoo48Wfc7q1autu7u74AEACDb1AersNGtrKxxXhkjjpfoEAQDQNM1S//GPf9i6dets3LhxBeM6fuaZZ4o+Z/bs2XbRRRfV6QoBAH4qZa1KbipkoH08WsZWThZHgU4i0b/nAgDQNEFQf8yaNcvtIfIoExTTYnIAQM2pqal6/uSXvFY2R8vdysnmKOCJx2t6iQAABDsI2myzzWzw4MH26quvFozrePz48UWfM3z4cPcAANQ/AOro6Nn0tKsrO86yNgBAWDR0T9CwYcNsr732svvvvz83tn79ene8zz77NPLSAAC+JXDKAPkDIPHGksnsPAAAgq7h1eG0tO1HP/qR3XTTTbZ06VI7/fTT7Z133nHV4gAAwaB9PPlL4IoFQul0dh4AAEHX8D1Bn/vc5+zvf/+7ff3rX7dXXnnFdt99d5s/f36PYgkAgMZRIYNqzgMAINJBkJx55pnuAQAIJlVyq+Y8AAAivRwOABB8KmWtKnD+ZqcejatQp+YBABB0BEEAgLJKW6sMtvgDIe84laLnDwAgHAiCAABlUflrlcFuayscV4aI8tgAgDAJxJ4gAED9qZy1qrmpmIH28mgpW6lMjgKdRKLy5wEAECQEQQAQ0can6vuTX/ZaGR0teSuV0VHAE4/X/BIBAKgZlsMBQAQDoI6Onn1/urqy4zoPAEAzIwgCgIgtgVMGSM1N/byxZDI7DwCAZkUQBAARor08/gyQPxBKp7PzAABoVgRBABAhKmZQzXkAAIQRQRAARIiquVVzHgAAYUQQBAARonLWqgLnb3jq0Xgslp0HAECzIggCgAhReWuVwRZ/IOQdp1L0/QEANDeCIACIGPUB6uw0a2srHFeGSOOl+gQBABB2NEsFgJBTOWtVc1MxA+3l0VK2UpkcBTqJROXPAwCgGRAEAUCIqbGp+v7kl71WRkdL3kpldBTwxOM1v0QAAAKH5XAAEOIAqKOjZ9+frq7suM4DAICeCIIAIKRL4JQBUnNTP28smczOAwAAhQiCACCEtJfHnwHyB0LpdHYeAAAoRBAEACGkYgbVnAcAQJQQBAFACKmaWzXnAQAQJQRBABBCKmetKnD+hqcejcdi2XkAAKAQQRAAhJDKW6sMtvgDIe84laLvDwAAxRAEAUBIqQ9QZ6dZW1vhuDJEGi/VJwgAgKiiWSoABIBKWauSmwoZaB+PlrGVk8VRoJNI9O+5AABEFUEQADSYmpqq509+yWtlc7TcrZxsjgKeeLymlwgAQFNhORwANDgA6ujo2fOnqys7rvMAAKC6CIIAoIFL4JQBUmNTP28smczOAwAA1UMQBAANon08/gyQPxBKp7PzAABA9RAEAUCDqJBBNecBAIDyEAQBQIOokls15wEAgPIQBAFAg6iUtarA+ZudejQei2XnAQCA6iEIAoAGUWlrlcEWfyDkHadS9PwBAKDaCIIAoIHUB6iz06ytrXBcGSKNl9MnCAAAVIZmqQBQRSpnrWpuKmagvTxaylYqk6NAJ5Go/HkAAKB/CIIAoErU2FR9f/LLXiujoyVvpTI6Cnji8ZpfIgAAYDkcAFQvAOro6Nn3p6srO67zAAAgGAiCAKAKS+CUAVJzUz9vLJnMzgMAAI1HEAQAA6S9PP4MkD8QSqez8wAAQOMRBAHAAKmYQTXnAQCA2iIIAoABUjW3as4DAAC1RRAEAAOkctaqAudveOrReCyWnQcAABqPIAgABkjlrVUGW/yBkHecStH3BwCAoCAIAoAqUB+gzk6ztrbCcWWINF6qTxAAAKgfmqUCQBEqZ61qbipmoL08WspWKpOjQCeRqPx5AACgvgiCAMBHjU3V9ye/7LUyOlryViqjo4AnHq/5JQIAgAFgORwA+AKgjo6efX+6urLjOg8AAMKNIAgA8pbAKQOk5qZ+3lgymZ0HAADCiyAIAD6gvTz+DJA/EEqns/MAAEB4EQQBwAdUzKCa8wAAQDARBAHAB1TNrZrzAABAMBEEAcAHVM5aVeD8DU89Go/FsvMAAEB41SQIWrFihZ188sm21VZb2YgRI2ybbbaxCy64wNasWVMwp6Wlpcdj8eLFtbgkAChJ5a1VBlv8gZB3nErR9wcAgLCrSZ+gZ555xtavX2/XXnutbbvttvb000/bKaecYu+8845dfvnlBXPvu+8+22mnnXLHo0ePrsUlAUBZ1Aeos7N4nyAFQKX6BAEAgOBryWSKFYOtvssuu8x+8IMf2N/+9rdcJkiZoj/84Q+2++679/t1u7u7bdSoUbZq1SprbW2t4hUDCDuVslYlNxUy0D4eLWMrN4szkOcCAIDGKDc2qEkmqBhdyKabbtpj/DOf+Yy99957tt1229lXvvIVd9yX1atXu0f+NwoAfmpqWiybo+Vu5WRzFPDE4zW9RAAA0MyFEZYtW2ZXXXWVffGLX8yNbbTRRnbFFVfYHXfcYb/85S9tv/32s8MPP9zuvvvuPl9r9uzZLrrzHjHtUgYAXwDU0dGz509XV3Zc5wEAQHRVtBzu/PPPt+985zt9zlm6dKntsMMOueOuri478MADLR6P23XXXdfnc0844QRbvny5PdRHJ8JimSAFQiyHA+AtY2tv773pqQocKCO0fDnL2wAAaDY1WQ43c+ZMmz59ep9ztt5669x/v/zyyzZ58mTbd9997Yc//GHJ1580aZItWLCgzznDhw93DwAoRr9D6S0AEv3aJ53OzmO5GwAA0VRREDRmzBj3KIcyQAqA9tprL5s7d64NGlR65d2SJUtsAl0IAQyAChlUcx4AAGg+NSmMoABIy98mTpzoSmL//e9/z50bP368+3rTTTfZsGHDbI899nDH8+bNsxtuuKHkkjkA6Eu5v0fh9y0AAERXTYIgLWlTMQQ9ttDi+zz5W5C+8Y1v2AsvvGBDhgxx+4huu+0269CuZQDoJ5Wy1o8dFUEotuPR2xOkeQAAIJrq1ieoVugTBKC36nCS/xNOAZCoGSpNTwEAaD7lxgZ1KZENAPWkAEeBTltb4bgyQARAAACgbs1SAWAgZa9VzU3FDLSXR0vZSpW3VqCTSFT+PAAA0PwIggAEfmnbjBmFZa+V0Zkzp3RGRwEPZbABAIAfy+EABH5vj7/vj4oeaFznAQAAKkUQBCCwS+CUASpWusUbSyaz8wAAACpBEAQgkLSXx58B8gdC6XR2HgAAQCUIggAEkooZVHMeAACAhyAIQCCpmls15wEAAHgIggAEkspZqwqc1+DUT+OxWHYeAABAJQiCAASSylurDLb4AyHvOJWi7w8AAKgcQRCAwFIfoM5Os7a2wnFliDReqk8QAABAMTRLBVAXKmWtSm4qZKB9PFrGVk4WR4FOItG/5wIAABRDEASg5tTUVD1/8kteK5uj5W7lZHMU8MTjNb1EAAAQISyHA1DzAKijo2fPn66u7LjOAwAA1BNBEICaLoFTBkiNTf28sWQyOw8AAKBeCIIA1Iz28fgzQPkUCKXT2XkAAAD1QhAEoGZUyKCa8wAAAKqBIAhAzaiSWzXnAQAAVANBEICaUSlrVYHzNzv1aDwWy84DAACoF4IgADWj0tYqgy3+QMg7TqXo+QMAAOqLIAhATakPUGenWVtb4bgyRBovp08QAABANdEsFUBFVM5a1dxUzEB7ebSUrVQmR4FOIlH58wAAAGqBIAhA2dTYVH1/8steK6OjJW+lMjoKeOLxml8iAABASSyHA1B2ANTR0bPvT1dXdlznAQAAwoAgCEBZS+CUAVJzUz9vLJnMzgMAAAg6giAAJWkvjz8D5A+E0unsPAAAgKAjCAJQkooZVHMeAABAIxEEAShJ1dyqOQ8AAKCRCIIAlKRy1qoC52946tF4LJadBwAAEHQEQQBKUnlrlcEWfyDkHadS9P0BAADhQBAEoCzqA9TZadbWVjiuDJHGS/UJAgAACAqapQIRpXLWquamYgbay6OlbKUyOQp0EonKnwcAABAkBEFABKmxqfr+5Je9VkZHS95KZXQU8MTjNb9EAACAmmE5HBDBAKijo2ffn66u7LjOAwAANDOCICBiS+CUAVJzUz9vLJnMzgMAAGhWBEFAhGgvjz8D5A+E0unsPAAAgGZFEAREiIoZVHMeAABAGBEEARGiam7VnAcAABBGBEFAhKictarA+RueejQei2XnAQAANCuCICBCVN5aZbDFHwh5x6kUfX8AAEBzIwgCIkZ9gDo7zdraCseVIdJ4qT5BAAAAYUezVCDEVMpaldxUyED7eLSMrZwsjgKdRKJ/zwUAAAg7giAgpNTUVD1/8kteK5uj5W7lZHMU8MTjNb1EAACAQGI5HBDSAKijo2fPn66u7LjOAwAAoDiCICCES+CUAVJjUz9vLJnMzgMAAEBPBEFAyGgfjz8D5A+E0unsPAAAAPREEASEjAoZVHMeAABA1BAEASGjSm7VnAcAABA1BEFAyKiUtarA+ZudejQei2XnAQAAoCeCICBkVNpaZbDFHwh5x6kUPX8AAAB6QxAEhJD6AHV2mrW1FY4rQ6TxcvoEAQAARBXNUoEAUDlrVXNTMQPt5dFStlKZHAU6iUTlzwMAAIi6mmWC2tvbraWlpeBxySWXFMx56qmnbP/997cNNtjAYrGYXXrppbW6HCCw1Ni0vd1s8mSzadOyX3VcTsNTBTzxuNkxx2S/EgABAAA0OBN08cUX2ymnnJI7HjlyZO6/u7u77ZBDDrGDDz7YrrnmGvvTn/5kn//8523jjTe2U089tZaXBQSGAp2Ojp6NT7u6suMsbQMAAAhZEKSgZ/z48UXP3XzzzbZmzRq74YYbbNiwYbbTTjvZkiVL7MorryQIQmSWwM2Y0TMAEo2pyEEymV3yRoYHAAAgJIURtPxt9OjRtscee9hll11m77//fu7co48+agcccIALgDxTpkyxZ5991t54441eX3P16tUui5T/AMJIe3leeqn38wqE0unsPAAAAIQgE3T22WfbnnvuaZtuuqk98sgjNmvWLFu5cqXL9Mgrr7xiW221VcFzxo0blzu3ySabFH3d2bNn20UXXVSrywbqRsUMqjkPAAAANcgEnX/++T2KHfgfzzzzjJt77rnnWjwet1133dVOO+00u+KKK+yqq65ymZyBUDC1atWq3COtX5UDIaRqbtWcBwAAgBpkgmbOnGnTp0/vc87WW29ddHzSpEluOdyKFSts++23d3uFXn311YI53nFv+4hk+PDh7gGEncpZq6+PiiAU2xekPUE6r3kAAABoUBA0ZswY9+gPFT0YNGiQjR071h3vs88+9l//9V+2du1aGzp0qBtbsGCBC5B6WwoHNBMVO5gzJ1sFTgFPfiCkY0mlKIoAAAAQisIIKnqQSqXsj3/8o/3tb39zleDOOeccO+6443IBzrRp01xRhJNPPtn+/Oc/22233WZz5sxxy+iAqFD5a5XBbmsrHFcGiPLYAAAAtdGSyRRbiDMwTz75pH3pS19y+4O0B0gFEI4//ngX4OQvZVOz1DPOOMMef/xx22yzzeyss86y8847r6I/S9XhRo0a5fYHtba2VvtbASoqea1KbipkoH08WsZWbhZnIM8FAABAZbFBTYKgeiIIQlCanqrnT37Ja2VztNyNbA4AAECwYoOa9gkCohIAaV+Pv+ePCh5oXOcBAAAQHARBwABoGZsyQMXyqd5YMpmdBwAAgGAgCAIGQPt4/BkgfyCkVlaaBwAAgGAgCAIGQIUMqjkPAAAAtUcQBAyAKrlVcx4AAABqjyAIGACVslYVOK+5qZ/GY7HsPAAAAAQDQRAwAOrlozLY4g+EvONUip4/AAAAQUIQBAyQ+gB1dpq1tRWOK0OkcfoEAQAABMuQRl8AEDQqZ61qbipmoL08WspWKpOjQCeRqPx5AAAAqD+CICCPGpuq709+2WtldLTkrVRGRwFPPF7zSwQAAMAAsRwOyAuAOjp69v3p6sqO6zwAAADCjyAI+GAJnDJAam7q540lk9l5AAAACDeCIMCye3n8GSB/IJROZ+cBAAAg3AiCAMsWM6jmPAAAAAQXQRBg2Wpu1ZwHAACA4CIIAixbzlpV4PwNTz0aj8Wy8wAAABBuBEHAB+WtVQZb/IGQd5xK0fcHAACgGRAEAR9QH6DOTrO2tsJxZYg0XqpPEAAAAMKBZqloWipnrWpuKmagvTxaylYqk6NAJ5Go/HkAAAAID4IgNCU1NlXfn/yy18roaMlbqYyOAp54vOaXCAAAgAZhORyaMgDq6OjZ96erKzuu8wAAAIgugiA0FS2BUwZIzU39vLFkMjsPAAAA0UQQhKaivTz+DJA/EEqns/MAAAAQTQRBaCoqZlDNeQAAAGg+BEFoKqrmVs15AAAAaD4EQWgqKmetKnD+hqcejcdi2XkAAACIJoIgNBWVt1YZbPEHQt5xKkXfHwAAgCgjCELTUR+gzk6ztrbCcWWINF6qTxAAAACaG81SEWgqZa1KbipkoH08WsZWThZHgU4i0b/nAgAAoLkRBCGw1NRUPX/yS14rm6PlbuVkcxTwxOM1vUQAAACEEMvhENgAqKOjZ8+frq7suM4DAAAA/UEQhEAugVMGSI1N/byxZDI7DwAAAKgUQRACR/t4/BkgfyCUTmfnAQAAAJUiCELgqJBBNecBAAAA+QiCEDiq5FbNeQAAAEA+giAEjkpZqwqcv9mpR+OxWHYeAAAAUCmCIASOSlurDLb4AyHvOJWi5w8AAAD6hyAIgaQ+QJ2dZm1thePKEGm8nD5BAAAAQDE0S0VdqJy1qrmpmIH28mgpW6lMjgKdRKLy5wEAAAB9IQhCzamxqfr+5Je9VkZHS95KZXQU8MTjNb9EAAAARAjL4VDzAKijo2ffn66u7LjOAwAAAPVEEISaLoFTBkjNTf28sWQyOw8AAACoF4Ig1Iz28vgzQP5AKJ3OzgMAAADqhSAINaNiBtWcBwAAAFQDQRBqRtXcqjkPAAAAqAaCINSMylmrCpy/4alH47FYdh4AAABQLwRBqBmVt1YZbPEHQt5xKkXfHwAAANQXQRBqSn2AOjvN2toKx5Uh0nipPkEAAABAtdEsFRVROWtVc1MxA+3l0VK2UpkcBTqJROXPAwAAAGqBIAhlU2NT9f3JL3utjI6WvJXK6CjgicdrfokAAABASSyHQ9kBUEdHz74/XV3ZcZ0HAAAAIhsELVq0yFpaWoo+Hn/8cTdnxYoVRc8vXry4FpeEAS6BUwZIzU39vLFkMjsPAAAAiORyuH333ddW+jpgfu1rX7P777/f9t5774Lx++67z3baaafc8ejRo2txSRgA7eXxZ4D8gVA6nZ3HkjcAAABEMggaNmyYjR8/Pne8du1a+9nPfmZnnXWWy/bkU9CTPxfB44tnBzwPAAAAaPo9QXfffbe9/vrrdtJJJ/U495nPfMbGjh1r++23n5tXyurVq627u7vggdpSNbdqzgMAAACaPgi6/vrrbcqUKbaFSol9YKONNrIrrrjC7rjjDvvlL3/pgqDDDz+8ZCA0e/ZsGzVqVO4Ri8Xq8B1Em8pZ66/O3/DUo3H9NWgeAAAAEHQtmUyx7e7FnX/++fad73ynzzlLly61HXbYIXf80ksv2cSJE+3222+3o446qs/nnnDCCbZ8+XJ7SJtL+sgE6eFRJkiB0KpVq6y1tbXcbwX9rA4n+XeMFxjR+BQAAACNpthAiZJSsUFFe4Jmzpxp06dP73PO1ltvXXA8d+5ct+9Hy95KmTRpki1YsKDPOcOHD3cP1JcCHAU6xfoEpVIEQAAAAAiPioKgMWPGuEe5lGRSEKQMz9ChQ0vOX7JkiU1gY0nNqZS1km0qZKC3W8vY1My0FAU6iUT/ngsAAAA0dXU4zwMPPOCWt33hC1/oce6mm25yVeT22GMPdzxv3jy74YYb7LrrrqvlJUWelrUVy+bMmVNeNkcBD2WwAQAAEGZDal0QQT2D8vcI5fvGN75hL7zwgg0ZMsTNue2226zD23iCmu3r8e8C6+rKjrOvBwAAAFFQUWGEMG9+ijotgWtv773pqQocKCO0fDnL2wAAANDcsUFdSmSj8bSPp7cASBQKp9PZeQAAAEAzIwiKCBUyqOY8AAAAIKwIgiKi3KJ7FOcDAABAsyMIigiVstaeH6+5qZ/GY7HsPAAAAKCZEQRFhIodqAy2+AMh71hNTymKAAAAgGZHEBQhKn+tMthtbYXjyhBRHhsAAABRUdM+Qah92WtVc1MxA+3l0VK2UpkcBTqJROXPAwAAAJoFQVCIG5/OmFFY9loZHS15K5XRUcATj9f8EgEAAIBAYjlcSAOgjo6efX+6urLjOg8AAACgOIKgEC6BUwZIzU39vLFkMjsPAAAAQE8EQSGjvTz+DJA/EEqns/MAAAAA9EQQFDIqZlDNeQAAAEDUEASFjKq5VXMeAAAAEDUEQSGjctaqAudveOrReCyWnQcAAACgJ4KgkFF5a5XBFn8g5B2nUvT9AQAAAHpDEBRC6gPU2WnW1lY4rgyRxkv1CQIAAACijGapDaZS1qrkpkIG2sejZWzlZHEU6CQS/XsuAAAAEGUEQQ2kpqbq+ZNf8lrZHC13Kyebo4AnHq/pJQIAAABNh+VwDQyAOjp69vzp6sqO6zwAAACA6iMIatASOGWA1NjUzxtLJrPzAAAAAFQXQVADaB+PPwPkD4TS6ew8AAAAANVFENQAKmRQzXkAAAAAykcQ1ACq5FbNeQAAAADKRxDUACplrSpw/manHo3HYtl5AAAAAKqLIKgBVNpaZbDFHwh5x6kUPX8AAACAWiAIahD1AersNGtrKxxXhkjj5fQJAgAAAFA5mqVWicpZq5qbihloL4+WspXK5CjQSSQqfx4AAACA/iMIqgI1NlXfn/yy18roaMlbqYyOAp54vOaXCAAAAOADLIerQgDU0dGz709XV3Zc5wEAAAAEB0HQAJfAKQOk5qZ+3lgymZ0HAAAAIBgIggZAe3n8GSB/IJROZ+cBAAAACAaCoAFQMYNqzgMAAABQewRBA6BqbtWcBwAAAKD2CIIGQOWsVQXO3/DUo/FYLDsPAAAAQDAQBA2AylurDLb4AyHvOJWi7w8AAAAQJARBA6Q+QJ2dZm1thePKEGm8VJ8gAAAAAPVFs9QqUKCTSGSrwKkIgvYAaQkcGSAAAAAgeAiCqkQBTzze6KsAAAAAUArL4QAAAABECkEQAAAAgEghCAIAAAAQKQRBAAAAACKFIAgAAABApBAEAQAAAIgUgiAAAAAAkUIQBAAAACBSCIIAAAAARApBEAAAAIBIIQgCAAAAECkEQQAAAAAihSAIAAAAQKQQBAEAAACIFIIgAAAAAJFCEAQAAAAgUoZYyGUyGfe1u7u70ZcCAAAAoIG8mMCLEZo2CHrrrbfc11gs1uhLAQAAABCQGGHUqFG9nm/JlAqTAm79+vX28ssv28iRI62lpaXhkaeCsXQ6ba2trQ29lmbG+1wfvM/1wftcH7zPtcd7XB+8z/XB+xze91mhjQKgzTff3AYNGtS8mSB9c1tssYUFif4S+T9M7fE+1wfvc33wPtcH73Pt8R7XB+9zffA+h/N97isD5KEwAgAAAIBIIQgCAAAAECkEQVU0fPhwu+CCC9xX1A7vc33wPtcH73N98D7XHu9xffA+1wfvc/O/z6EvjAAAAAAAlSATBAAAACBSCIIAAAAARApBEAAAAIBIIQgCAAAAECkEQQAAAAAihSCon771rW/ZvvvuaxtuuKFtvPHGRee8+OKL9qlPfcrNGTt2rP3Hf/yHvf/++wVzFi1aZHvuuacrDbjtttvajTfeWKfvIHz0XrW0tBR9PP74427OihUrip5fvHhxoy8/VNrb23u8h5dccknBnKeeesr2339/22CDDSwWi9mll17asOsNI92rJ598sm211VY2YsQI22abbVyZ0DVr1hTM4X4euKuvvtrd07pXJ02aZL/73e8afUmhNnv2bPvoRz9qI0eOdP+2HX744fbss88WzInH4z3u29NOO61h1xxGF154YY/3cIcddsidf++99+yMM86w0aNH20YbbWRHHXWUvfrqqw295mb5904PvbfCvVy53/zmN/bpT3/aNt98c/d+3XXXXQXnVZj661//uk2YMMH9+3fwwQfbc889VzDnn//8px177LHW2trqPmfr38u3337bqokgqJ/0QeWzn/2snX766UXPr1u3zgVAmvfII4/YTTfd5AIc/aV7li9f7uZMnjzZlixZYslk0r7whS/YvffeW8fvJDwUdK5cubLgofdLHyL33nvvgrn33Xdfwby99tqrYdcdVhdffHHBe3jWWWflznV3d9shhxxiEydOtCeeeMIuu+wy9w/2D3/4w4Zec5g888wztn79erv22mvtz3/+s333u9+1a665xv7zP/+zx1zu5/677bbb7Nxzz3UB5pNPPmm77babTZkyxV577bVGX1poPfjgg+4DooLxBQsW2Nq1a93Pg3feeadg3imnnFJw3/KLksrttNNOBe/hww8/nDt3zjnn2M9//nO744473N/Jyy+/bEceeWRDrzeM9EvU/PdY97ToM56He7ky+lmgn7X6BVQxev++973vuX/zHnvsMfvQhz7kfi4rsPcoANK/jfr7+MUvfuECq1NPPdWqSn2C0H9z587NjBo1qsf4r371q8ygQYMyr7zySm7sBz/4Qaa1tTWzevVqd/yVr3wls9NOOxU873Of+1xmypQpdbjy8FuzZk1mzJgxmYsvvjg3tnz5cvW9yvzhD39o6LWF3cSJEzPf/e53ez3//e9/P7PJJpvk7mU577zzMttvv32drrA5XXrppZmtttoqd8z9PHAf+9jHMmeccUbueN26dZnNN988M3v27IZeVzN57bXX3H364IMP5sYOPPDAzIwZMxp6XWF3wQUXZHbbbbei5958883M0KFDM3fccUdubOnSpe7v4dFHH63jVTYf3bfbbLNNZv369e6Ye3lgdE/eeeeduWO9r+PHj89cdtllBffz8OHDM7feeqs7/stf/uKe9/jjj+fm3HPPPZmWlpZMV1dXplrIBNXIo48+arvssouNGzcuN6YoV79BV2TrzVEKMJ/maByl3X333fb666/bSSed1OPcZz7zGbdMY7/99nPzUDktf9Myiz322MNlevKXcuoePeCAA2zYsGEF966WxLzxxhsNuuLwW7VqlW266aY9xrmf+0eZeGUq83/ODho0yB3zc7a69634792bb77ZNttsM9t5551t1qxZ9u677zboCsNLS4S0pGjrrbd2vxnXMnvRfa0MXP69raVyW265Jff2AH9m/OQnP7HPf/7zbhmXh3u5erQK6pVXXim4d0eNGuWWKnv3rr5qCVz+Kh/N189vZY6qZUjVXgkF9BecHwCJd6xzfc1RoPSvf/3LrZNE766//nr3wXuLLbbIjWld9BVXXGGf+MQn3P9ZfvrTn7r16lqPqg+SKM/ZZ5/t9qrpQ42Wc+qHvpYAXHnllbl7V8sQe7u/N9lkk4Zcd5gtW7bMrrrqKrv88stzY9zPA/OPf/zDLU0u9nNWyxExcFrSqaXcukf1AdEzbdo0t1xWH+C1f/C8885zvySZN29eQ683TPShUMvot99+e/fz96KLLnL7MJ9++mn3c1a/hPLvSda97X3GQOX0s/XNN9+06dOn58a4l6vLuz+L/VzO/3ysX/zlGzJkiPtMUs37myAoz/nnn2/f+c53+pyzdOnSgo2JaMz7/tJLL7m9U7fffnvBPP2mRuv/Pdq8q3XSymRE/UNjJe9z/nu46667un9sv/jFL7oN0Srigerez11dXTZ16lS3Bl1rzz3czwg67Q3Sh/L8vSqSv3ZfqyK0Afqggw6y559/3hUBQWmHHnpowc9hBUX6MK5/9/glae1+uar3XQGPh3u5eREE5Zk5c2ZB9F+MUtLlGD9+fI8KRF7VFp3zvvoruehYlTCi9AOuP+/73Llz3VKtcj4I6h8Ob6NjlA3k/tZ7qOVwqlam30r2du/m399RVen7rKBGxVFU+KOcwhLcz+VTEDl48OCi92rU79NqOPPMM3MblvMz8r3dt17Gkw+O/aOsz3bbbefew09+8pNu6ZayFvnZIO7t/nvhhRdcEZpSGR7u5YHx7k/dqwooPTrefffdc3P8xWv0GUQV46p5fxME5RkzZox7VMM+++zjymjrL9FL6emDiwKcj3zkI7k5v/rVrwqepzkaj5JK33fts1MQdMIJJ9jQoUNLzlflvfz/o0XVQO5vvYdajuXdy7pH/+u//sutSff+DnTvKkCK+lK4St5nZYAUAKnam+5pvcelcD+XTxlMvbf333+/W0boLd/SsT7Ao3/0M1jVIu+8807XusC/NLa3+1a4d/tP5YGVfTj++OPdfa2fvbqXVRpbtERLe4ai9hmiWvQzWP/GqWpvX7iXB0Y/LxTI6N71gh5tA9FeH6/isu5hBfja++ZVQ33ggQfcz28vCK2KqpVYiJgXXnjBVWy66KKLMhtttJH7bz3eeustd/7999/P7LzzzplDDjkks2TJksz8+fNdJbNZs2blXuNvf/tbZsMNN8z8x3/8h6vqcvXVV2cGDx7s5qJ39913n6saovfM78Ybb8zccsst7pwe3/rWt1yVvhtuuKEh1xpGjzzyiKsMp/v2+eefz/zkJz9x9+4JJ5xQUMll3LhxmeOPPz7z9NNPZ/73f//X3cvXXnttQ689TF566aXMtttumznooIPcf69cuTL38HA/D5zuTVUd0nupikOnnnpqZuONNy6o3InKnH766a4q6qJFiwru23fffdedX7Zsmava+fvf/95VOPzZz36W2XrrrTMHHHBAoy89VGbOnOneY72Hv/3tbzMHH3xwZrPNNnPV+OS0007LbLnllpkHHnjAvdf77LOPe6Byqhqp91JVTvNxL/ePPgt7n4v1ee3KK690/63PznLJJZe4n8N6P5966qlMIpFwlVH/9a9/5V5j6tSpmT322CPz2GOPZR5++OHMhz/84cwxxxyTqSaCoH468cQT3V+s/7Fw4cLcnBUrVmQOPfTQzIgRI9wPLv1AW7t2bcHraP7uu++eGTZsmPs/lkpuo2/6P8G+++5b9Jw+6Oy4447uA7nKkas8bn4JUZT2xBNPZCZNmuQ+5GywwQbu/fz2t7+dee+99wrm/fGPf8zst99+7gNmW1ub+6GG8un/68V+huT/bor7uTquuuoq9wFHP2f1Hi5evLjRlxRqvd233r9fL774ovuQuOmmm7qfDwr29cu+VatWNfrSQ0UtMyZMmODuW/2M1bE+lHv0gfFLX/qSa1egnxFHHHFEwS9RUL57773X3cPPPvtswTj3cv/os22xnxH67OyVyf7a177mfpmq91W/DPS/96+//rr7vKdEg/79O+mkk3KJhmpp0f9UL68EAAAAAMFGnyAAAAAAkUIQBAAAACBSCIIAAAAARApBEAAAAIBIIQgCAAAAECkEQQAAAAAihSAIAAAAQKQQBAEAAACIFIIgAAAAAJFCEAQAAAAgUgiCAAAAAFiU/H8evd3MYTUM7wAAAABJRU5ErkJggg==", "text/plain": ["<Figure size 1000x700 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["def plot_predictions(train_data=X_train, \n", "                     train_labels=y_train, \n", "                     test_data=X_test, \n", "                     test_labels=y_test, \n", "                     predictions=y_preds):\n", "  \"\"\"\n", "  Plots training data, test data and compares predictions.\n", "  \"\"\"\n", "  plt.figure(figsize=(10, 7))\n", "  # Plot training data in blue\n", "  plt.scatter(train_data, train_labels, c=\"b\", label=\"Training data\")\n", "  # Plot test data in green\n", "  plt.scatter(test_data, test_labels, c=\"g\", label=\"Testing data\")\n", "  # Plot the predictions in red (predictions were made on the test data)\n", "  plt.scatter(test_data, predictions, c=\"r\", label=\"Predictions\")\n", "  # Show the legend\n", "  plt.legend();\n", "\n", "\n", "\n", "plot_predictions(train_data=X_train,\n", "                 train_labels=y_train,\n", "                 test_data=X_test,\n", "                 test_labels=y_test,\n", "                 predictions=y_preds)\n", "     "]}, {"cell_type": "code", "execution_count": 17, "id": "3e696138", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 150ms/step - loss: 6.1873 - mae: 6.1873\n"]}, {"data": {"text/plain": ["[6.187339782714844, 6.187339782714844]"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["model.evaluate(X_test, y_test)"]}, {"cell_type": "code", "execution_count": 19, "id": "30a84993", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([ 70,  74,  78,  82,  86,  90,  94,  98, 102, 106])"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "# Check the test label tensor values\n", "y_test"]}, {"cell_type": "code", "execution_count": null, "id": "44a24858", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[ 63.26094 ],\n", "       [ 67.383545],\n", "       [ 71.50615 ],\n", "       [ 75.62875 ],\n", "       [ 79.75136 ],\n", "       [ 83.87396 ],\n", "       [ 87.99657 ],\n", "       [ 92.11917 ],\n", "       [ 96.241776],\n", "       [100.36438 ]], dtype=float32)"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "# Check the predictions tensor values \n", "y_preds"]}, {"cell_type": "code", "execution_count": 21, "id": "011f0b91", "metadata": {}, "outputs": [{"data": {"text/plain": ["((10,), (10, 1))"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["# Check the tensor shapes\n", "y_test.shape, y_preds.shape"]}, {"cell_type": "code", "execution_count": 32, "id": "61a1568d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Epoch 1/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 28ms/step - loss: 77.5432 - mae: 77.5432 \n", "Epoch 2/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 23ms/step - loss: 42.8558 - mae: 42.8558\n", "Epoch 3/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 22ms/step - loss: 12.5848 - mae: 12.5848\n", "Epoch 4/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 22ms/step - loss: 8.4242 - mae: 8.4242\n", "Epoch 5/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 26ms/step - loss: 8.9378 - mae: 8.9378\n", "Epoch 6/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 35ms/step - loss: 8.8264 - mae: 8.8264\n", "Epoch 7/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 21ms/step - loss: 8.5599 - mae: 8.5599\n", "Epoch 8/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 22ms/step - loss: 9.3171 - mae: 9.3171\n", "Epoch 9/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 20ms/step - loss: 8.4077 - mae: 8.4077\n", "Epoch 10/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 23ms/step - loss: 9.0953 - mae: 9.0953\n", "Epoch 11/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 31ms/step - loss: 8.0955 - mae: 8.0955\n", "Epoch 12/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 18ms/step - loss: 9.7208 - mae: 9.7208\n", "Epoch 13/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 22ms/step - loss: 8.0768 - mae: 8.0768\n", "Epoch 14/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 35ms/step - loss: 9.7079 - mae: 9.7079\n", "Epoch 15/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 19ms/step - loss: 8.0581 - mae: 8.0581\n", "Epoch 16/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 18ms/step - loss: 9.6950 - mae: 9.6950\n", "Epoch 17/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 19ms/step - loss: 8.0394 - mae: 8.0394\n", "Epoch 18/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 23ms/step - loss: 9.6820 - mae: 9.6820\n", "Epoch 19/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 22ms/step - loss: 8.0207 - mae: 8.0207\n", "Epoch 20/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 17ms/step - loss: 9.6691 - mae: 9.6691\n", "Epoch 21/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 24ms/step - loss: 8.0020 - mae: 8.0020\n", "Epoch 22/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 19ms/step - loss: 9.6562 - mae: 9.6562\n", "Epoch 23/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 21ms/step - loss: 7.9833 - mae: 7.9833\n", "Epoch 24/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 26ms/step - loss: 9.6432 - mae: 9.6432\n", "Epoch 25/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 19ms/step - loss: 7.9646 - mae: 7.9646\n", "Epoch 26/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 19ms/step - loss: 9.6303 - mae: 9.6303\n", "Epoch 27/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 18ms/step - loss: 7.9460 - mae: 7.9460\n", "Epoch 28/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 18ms/step - loss: 9.6173 - mae: 9.6173\n", "Epoch 29/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 22ms/step - loss: 7.9273 - mae: 7.9273\n", "Epoch 30/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 20ms/step - loss: 9.6044 - mae: 9.6044\n", "Epoch 31/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 17ms/step - loss: 7.9086 - mae: 7.9086\n", "Epoch 32/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 18ms/step - loss: 9.5915 - mae: 9.5915\n", "Epoch 33/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 26ms/step - loss: 7.8899 - mae: 7.8899\n", "Epoch 34/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 18ms/step - loss: 9.5785 - mae: 9.5785\n", "Epoch 35/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 18ms/step - loss: 7.8712 - mae: 7.8712\n", "Epoch 36/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 18ms/step - loss: 9.5656 - mae: 9.5656\n", "Epoch 37/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 19ms/step - loss: 7.8525 - mae: 7.8525\n", "Epoch 38/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 19ms/step - loss: 9.5527 - mae: 9.5527\n", "Epoch 39/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 24ms/step - loss: 7.8338 - mae: 7.8338\n", "Epoch 40/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 19ms/step - loss: 9.5397 - mae: 9.5397\n", "Epoch 41/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 19ms/step - loss: 7.8151 - mae: 7.8151\n", "Epoch 42/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 18ms/step - loss: 9.5268 - mae: 9.5268\n", "Epoch 43/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 17ms/step - loss: 7.7965 - mae: 7.7965\n", "Epoch 44/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 29ms/step - loss: 9.5138 - mae: 9.5138\n", "Epoch 45/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 30ms/step - loss: 7.7778 - mae: 7.7778\n", "Epoch 46/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 20ms/step - loss: 9.5009 - mae: 9.5009\n", "Epoch 47/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 18ms/step - loss: 7.7591 - mae: 7.7591\n", "Epoch 48/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 16ms/step - loss: 9.4880 - mae: 9.4880\n", "Epoch 49/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 42ms/step - loss: 7.9277 - mae: 7.9277\n", "Epoch 50/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 24ms/step - loss: 8.6003 - mae: 8.6003\n", "Epoch 51/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 19ms/step - loss: 7.4470 - mae: 7.4470\n", "Epoch 52/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 19ms/step - loss: 10.3989 - mae: 10.3989\n", "Epoch 53/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 19ms/step - loss: 7.2077 - mae: 7.2077\n", "Epoch 54/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 30ms/step - loss: 11.4608 - mae: 11.4608\n", "Epoch 55/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 18ms/step - loss: 7.6666 - mae: 7.6666\n", "Epoch 56/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 17ms/step - loss: 9.3893 - mae: 9.3893\n", "Epoch 57/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 18ms/step - loss: 7.6480 - mae: 7.6480\n", "Epoch 58/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 19ms/step - loss: 9.3763 - mae: 9.3763\n", "Epoch 59/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 18ms/step - loss: 7.6293 - mae: 7.6293\n", "Epoch 60/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 19ms/step - loss: 9.3634 - mae: 9.3634\n", "Epoch 61/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 20ms/step - loss: 7.7979 - mae: 7.7979\n", "Epoch 62/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 24ms/step - loss: 8.4731 - mae: 8.4731\n", "Epoch 63/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 19ms/step - loss: 7.3176 - mae: 7.3176\n", "Epoch 64/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 17ms/step - loss: 10.2810 - mae: 10.2810\n", "Epoch 65/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 28ms/step - loss: 7.0792 - mae: 7.0792\n", "Epoch 66/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 18ms/step - loss: 11.3470 - mae: 11.3470\n", "Epoch 67/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 21ms/step - loss: 7.5368 - mae: 7.5368\n", "Epoch 68/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 18ms/step - loss: 9.2647 - mae: 9.2647\n", "Epoch 69/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 23ms/step - loss: 7.5181 - mae: 7.5181\n", "Epoch 70/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 26ms/step - loss: 9.2518 - mae: 9.2518\n", "Epoch 71/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 20ms/step - loss: 7.4995 - mae: 7.4995\n", "Epoch 72/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 18ms/step - loss: 9.3601 - mae: 9.3601\n", "Epoch 73/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 21ms/step - loss: 6.9330 - mae: 6.9330\n", "Epoch 74/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 20ms/step - loss: 9.4701 - mae: 9.4701\n", "Epoch 75/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 25ms/step - loss: 11.0339 - mae: 11.0339\n", "Epoch 76/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 26ms/step - loss: 11.9909 - mae: 11.9909\n", "Epoch 77/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 18ms/step - loss: 7.6618 - mae: 7.6618\n", "Epoch 78/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 19ms/step - loss: 8.3597 - mae: 8.3597\n", "Epoch 79/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 30ms/step - loss: 7.1588 - mae: 7.1588\n", "Epoch 80/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 23ms/step - loss: 10.2016 - mae: 10.2016\n", "Epoch 81/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 21ms/step - loss: 6.9091 - mae: 6.9091\n", "Epoch 82/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 18ms/step - loss: 11.3713 - mae: 11.3713\n", "Epoch 83/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 18ms/step - loss: 6.8884 - mae: 6.8884\n", "Epoch 84/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 20ms/step - loss: 11.2979 - mae: 11.2979\n", "Epoch 85/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 21ms/step - loss: 6.7331 - mae: 6.7331\n", "Epoch 86/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 18ms/step - loss: 8.0656 - mae: 8.0656\n", "Epoch 87/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 20ms/step - loss: 9.1750 - mae: 9.1750\n", "Epoch 88/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 20ms/step - loss: 10.6120 - mae: 10.6120\n", "Epoch 89/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 26ms/step - loss: 8.1415 - mae: 8.1415\n", "Epoch 90/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 23ms/step - loss: 9.2509 - mae: 9.2509\n", "Epoch 91/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 20ms/step - loss: 10.7539 - mae: 10.7539\n", "Epoch 92/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 18ms/step - loss: 12.0012 - mae: 12.0012\n", "Epoch 93/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 19ms/step - loss: 7.5538 - mae: 7.5538\n", "Epoch 94/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 30ms/step - loss: 8.2887 - mae: 8.2887\n", "Epoch 95/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 18ms/step - loss: 7.2242 - mae: 7.2242\n", "Epoch 96/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 21ms/step - loss: 9.0659 - mae: 9.0659\n", "Epoch 97/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 25ms/step - loss: 6.9798 - mae: 6.9798\n", "Epoch 98/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 21ms/step - loss: 10.3928 - mae: 10.3928\n", "Epoch 99/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 31ms/step - loss: 7.9654 - mae: 7.9654\n", "Epoch 100/100\n", "\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 35ms/step - loss: 9.0748 - mae: 9.0748\n"]}, {"data": {"text/plain": ["<keras.src.callbacks.history.History at 0x146ff89a690>"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["# Set random seed\n", "tf.random.set_seed(42)\n", "\n", "# Replicate original model\n", "model_1 = tf.keras.Sequential([\n", "  tf.keras.layers.Dense(1)\n", "])\n", "\n", "# Compile the model\n", "model_1.compile(loss=tf.keras.losses.mae,\n", "                optimizer=tf.keras.optimizers.SGD(),\n", "                metrics=['mae'])\n", "\n", "# Fit the model\n", "model_1.fit(tf.expand_dims(X_train, axis=-1), y_train, epochs=100)"]}, {"cell_type": "code", "execution_count": 33, "id": "befe57f5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 44ms/step\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x700 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Make and plot predictions for model_1\n", "y_preds_1 = model_1.predict(X_test)\n", "plot_predictions(predictions=y_preds_1)"]}, {"cell_type": "code", "execution_count": 34, "id": "b8cc50b4", "metadata": {}, "outputs": [{"data": {"text/plain": ["<keras.src.callbacks.history.History at 0x14683204380>"]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["# Set random seed\n", "tf.random.set_seed(42)\n", "\n", "# Replicate model_1 and add an extra layer\n", "model_2 = tf.keras.Sequential([\n", "  tf.keras.layers.Dense(1),\n", "  tf.keras.layers.Dense(1) # add a second layer\n", "])\n", "\n", "# Compile the model\n", "model_2.compile(loss=tf.keras.losses.mae,\n", "                optimizer=tf.keras.optimizers.SGD(),\n", "                metrics=['mae'])\n", "\n", "# Fit the model\n", "model_2.fit(tf.expand_dims(X_train, axis=-1), y_train, epochs=100, verbose=0) # set verbose to 0 for less output"]}, {"cell_type": "code", "execution_count": 35, "id": "cbb947c8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 60ms/step\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x700 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["\n", "# Make and plot predictions for model_2\n", "y_preds_2 = model_2.predict(X_test)\n", "plot_predictions(predictions=y_preds_2)"]}, {"cell_type": "code", "execution_count": 36, "id": "ccbbefc1", "metadata": {}, "outputs": [{"data": {"text/plain": ["<keras.src.callbacks.history.History at 0x146fe0d9550>"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["# Set random seed\n", "tf.random.set_seed(42)\n", "\n", "# Replicate model_2\n", "model_3 = tf.keras.Sequential([\n", "  tf.keras.layers.Dense(1),\n", "  tf.keras.layers.Dense(1)\n", "])\n", "\n", "# Compile the model\n", "model_3.compile(loss=tf.keras.losses.mae,\n", "                optimizer=tf.keras.optimizers.SGD(),\n", "                metrics=['mae'])\n", "\n", "# Fit the model (this time for 500 epochs, not 100)\n", "model_3.fit(tf.expand_dims(X_train, axis=-1), y_train, epochs=500, verbose=0) # set verbose to 0 for less output"]}, {"cell_type": "code", "execution_count": 37, "id": "66c1336b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 63ms/step\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x700 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Make and plot predictions for model_3\n", "y_preds_3 = model_3.predict(X_test)\n", "plot_predictions(predictions=y_preds_3)"]}, {"cell_type": "code", "execution_count": 39, "id": "23e2577f-7003-4f82-821e-70e797a829a8", "metadata": {}, "outputs": [{"data": {"text/plain": ["<keras.src.callbacks.history.History at 0x1468204bc50>"]}, "execution_count": 39, "metadata": {}, "output_type": "execute_result"}], "source": ["# Set random seed\n", "tf.random.set_seed(42)\n", "\n", "# Replicate model_2\n", "model_4 = tf.keras.Sequential([\n", "  tf.keras.layers.<PERSON><PERSON>(10),\n", "  tf.keras.layers.Dense(1)\n", "])\n", "\n", "# Compile the model\n", "model_4.compile(loss=tf.keras.losses.mae,\n", "                optimizer=tf.keras.optimizers.SGD(),\n", "                metrics=['mae'])\n", "\n", "# Fit the model (this time for 500 epochs, not 100)\n", "model_4.fit(tf.expand_dims(X_train, axis=-1), y_train, epochs=100, verbose=0) # set verbose to 0 for less output"]}, {"cell_type": "code", "execution_count": 40, "id": "91575aa3-6b33-454c-9614-f5381517a732", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 52ms/step\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x700 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Make and plot predictions for model_4\n", "y_preds_4 = model_4.predict(X_test)\n", "plot_predictions(predictions=y_preds_4)"]}, {"cell_type": "code", "execution_count": 41, "id": "97e3182b", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["WARNING:absl:You are saving your model as an HDF5 file via `model.save()` or `keras.saving.save_model(model)`. This file format is considered legacy. We recommend using instead the native Keras format, e.g. `model.save('my_model.keras')` or `keras.saving.save_model(model, 'my_model.keras')`. \n"]}], "source": ["# Save a model using the HDF5 format\n", "model_2.save(\"best_model_HDF5_format_1.h5\") # note the addition of '.h5' on the end"]}, {"cell_type": "code", "execution_count": null, "id": "e5b079b5-6da5-4e7e-982d-576c0dcac8a4", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "genai", "language": "python", "name": "genai"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.7"}}, "nbformat": 4, "nbformat_minor": 5}