{"cells": [{"cell_type": "code", "execution_count": 6, "id": "19eb4d8c-4619-4ccb-9983-9af06a9d9726", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2.20.0\n"]}], "source": ["# Import required libraries\n", "import tensorflow as tf\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "# check the version (should be 2.x+)\n", "print(tf.__version__) "]}, {"cell_type": "code", "execution_count": 7, "id": "0471cbda", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>age</th>\n", "      <th>sex</th>\n", "      <th>bmi</th>\n", "      <th>children</th>\n", "      <th>smoker</th>\n", "      <th>region</th>\n", "      <th>charges</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>19</td>\n", "      <td>female</td>\n", "      <td>27.900</td>\n", "      <td>0</td>\n", "      <td>yes</td>\n", "      <td>southwest</td>\n", "      <td>16884.92400</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>18</td>\n", "      <td>male</td>\n", "      <td>33.770</td>\n", "      <td>1</td>\n", "      <td>no</td>\n", "      <td>southeast</td>\n", "      <td>1725.55230</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>28</td>\n", "      <td>male</td>\n", "      <td>33.000</td>\n", "      <td>3</td>\n", "      <td>no</td>\n", "      <td>southeast</td>\n", "      <td>4449.46200</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>33</td>\n", "      <td>male</td>\n", "      <td>22.705</td>\n", "      <td>0</td>\n", "      <td>no</td>\n", "      <td>northwest</td>\n", "      <td>21984.47061</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>32</td>\n", "      <td>male</td>\n", "      <td>28.880</td>\n", "      <td>0</td>\n", "      <td>no</td>\n", "      <td>northwest</td>\n", "      <td>3866.85520</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   age     sex     bmi  children smoker     region      charges\n", "0   19  female  27.900         0    yes  southwest  16884.92400\n", "1   18    male  33.770         1     no  southeast   1725.55230\n", "2   28    male  33.000         3     no  southeast   4449.46200\n", "3   33    male  22.705         0     no  northwest  21984.47061\n", "4   32    male  28.880         0     no  northwest   3866.85520"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["# Read in the insurance dataset\n", "insurance = pd.read_csv(\"insurance.csv\")\n", "# Check out the insurance dataset\n", "insurance.head()"]}, {"cell_type": "code", "execution_count": 8, "id": "33a584f7-39fb-4716-b7c4-4db12620ee06", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 1338 entries, 0 to 1337\n", "Data columns (total 7 columns):\n", " #   Column    Non-Null Count  Dtype  \n", "---  ------    --------------  -----  \n", " 0   age       1338 non-null   int64  \n", " 1   sex       1338 non-null   object \n", " 2   bmi       1338 non-null   float64\n", " 3   children  1338 non-null   int64  \n", " 4   smoker    1338 non-null   object \n", " 5   region    1338 non-null   object \n", " 6   charges   1338 non-null   float64\n", "dtypes: float64(2), int64(2), object(3)\n", "memory usage: 73.3+ KB\n"]}], "source": ["insurance.info()"]}, {"cell_type": "markdown", "id": "12c764e2", "metadata": {}, "source": ["- We're going to have to turn the non-numerical columns into numbers (because a neural network can't handle non-numerical inputs). \n", "- To do so, we'll use the get_dummies() method in pandas. It converts categorical variables (like the sex, smoker and region columns)\n", "- into numerical variables using one-hot encoding."]}, {"cell_type": "code", "execution_count": 9, "id": "55d35287", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>age</th>\n", "      <th>bmi</th>\n", "      <th>children</th>\n", "      <th>charges</th>\n", "      <th>sex_female</th>\n", "      <th>sex_male</th>\n", "      <th>smoker_no</th>\n", "      <th>smoker_yes</th>\n", "      <th>region_northeast</th>\n", "      <th>region_northwest</th>\n", "      <th>region_southeast</th>\n", "      <th>region_southwest</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>19</td>\n", "      <td>27.900</td>\n", "      <td>0</td>\n", "      <td>16884.92400</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>18</td>\n", "      <td>33.770</td>\n", "      <td>1</td>\n", "      <td>1725.55230</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>28</td>\n", "      <td>33.000</td>\n", "      <td>3</td>\n", "      <td>4449.46200</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>33</td>\n", "      <td>22.705</td>\n", "      <td>0</td>\n", "      <td>21984.47061</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>32</td>\n", "      <td>28.880</td>\n", "      <td>0</td>\n", "      <td>3866.85520</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   age     bmi  children      charges  sex_female  sex_male  smoker_no  \\\n", "0   19  27.900         0  16884.92400        True     False      False   \n", "1   18  33.770         1   1725.55230       False      True       True   \n", "2   28  33.000         3   4449.46200       False      True       True   \n", "3   33  22.705         0  21984.47061       False      True       True   \n", "4   32  28.880         0   3866.85520       False      True       True   \n", "\n", "   smoker_yes  region_northeast  region_northwest  region_southeast  \\\n", "0        True             False             False             False   \n", "1       False             False             False              True   \n", "2       False             False             False              True   \n", "3       False             False              True             False   \n", "4       False             False              True             False   \n", "\n", "   region_southwest  \n", "0              True  \n", "1             False  \n", "2             False  \n", "3             False  \n", "4             False  "]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["# Turn all categories into numbers\n", "insurance_one_hot = pd.get_dummies(insurance)\n", "insurance_one_hot.head() # view the converted columns"]}, {"cell_type": "code", "execution_count": 10, "id": "5c0801d3", "metadata": {}, "outputs": [], "source": ["# Create X & y values\n", "X = insurance_one_hot.drop(\"charges\", axis=1)\n", "y = insurance_one_hot[\"charges\"]"]}, {"cell_type": "code", "execution_count": 11, "id": "927def7f", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>age</th>\n", "      <th>bmi</th>\n", "      <th>children</th>\n", "      <th>sex_female</th>\n", "      <th>sex_male</th>\n", "      <th>smoker_no</th>\n", "      <th>smoker_yes</th>\n", "      <th>region_northeast</th>\n", "      <th>region_northwest</th>\n", "      <th>region_southeast</th>\n", "      <th>region_southwest</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>19</td>\n", "      <td>27.900</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>18</td>\n", "      <td>33.770</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>28</td>\n", "      <td>33.000</td>\n", "      <td>3</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>33</td>\n", "      <td>22.705</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>32</td>\n", "      <td>28.880</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   age     bmi  children  sex_female  sex_male  smoker_no  smoker_yes  \\\n", "0   19  27.900         0        True     False      False        True   \n", "1   18  33.770         1       False      True       True       False   \n", "2   28  33.000         3       False      True       True       False   \n", "3   33  22.705         0       False      True       True       False   \n", "4   32  28.880         0       False      True       True       False   \n", "\n", "   region_northeast  region_northwest  region_southeast  region_southwest  \n", "0             False             False             False              True  \n", "1             False             False              True             False  \n", "2             False             False              True             False  \n", "3             False              True             False             False  \n", "4             False              True             False             False  "]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["# View features\n", "X.head()"]}, {"cell_type": "code", "execution_count": 12, "id": "c0896a98-224e-4389-9ab3-d72882ccfc70", "metadata": {}, "outputs": [{"data": {"text/plain": ["sex_female\n", "False    676\n", "True     662\n", "Name: count, dtype: int64"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["X[\"sex_female\"].value_counts()"]}, {"cell_type": "code", "execution_count": 14, "id": "8bf0406c", "metadata": {}, "outputs": [], "source": ["# Create training and test sets\n", "from sklearn.model_selection import train_test_split\n", "\n", "X_train, X_test, y_train, y_test = train_test_split(\n", "    X, y, test_size=0.2, random_state=42\n", ")"]}, {"cell_type": "code", "execution_count": 15, "id": "2e178293-2aaa-4433-bfbb-347dc60223fd", "metadata": {}, "outputs": [{"data": {"text/plain": ["sex_female\n", "False    548\n", "True     522\n", "Name: count, dtype: int64"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["X_train[\"sex_female\"].value_counts()"]}, {"cell_type": "code", "execution_count": 16, "id": "5c25b0de-a240-4554-a60b-e1ecb365b364", "metadata": {}, "outputs": [{"data": {"text/plain": ["sex_female\n", "True     140\n", "False    128\n", "Name: count, dtype: int64"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["X_test[\"sex_female\"].value_counts()"]}, {"cell_type": "code", "execution_count": 17, "id": "75ad5c1b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Epoch 1/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m1s\u001b[0m 3ms/step - loss: 8710.2988 - mae: 8710.2988  \n", "Epoch 2/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7577.1558 - mae: 7577.1558 \n", "Epoch 3/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 3ms/step - loss: 7591.9897 - mae: 7591.9897\n", "Epoch 4/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7455.5454 - mae: 7455.5454 \n", "Epoch 5/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7498.4385 - mae: 7498.4385 \n", "Epoch 6/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7654.4380 - mae: 7654.4380 \n", "Epoch 7/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7720.2515 - mae: 7720.2515 \n", "Epoch 8/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 3ms/step - loss: 7841.6904 - mae: 7841.6904\n", "Epoch 9/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7704.4985 - mae: 7704.4985 \n", "Epoch 10/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7407.6143 - mae: 7407.6143 \n", "Epoch 11/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 3ms/step - loss: 7428.9453 - mae: 7428.9453\n", "Epoch 12/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7543.5747 - mae: 7543.5747 \n", "Epoch 13/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7437.8994 - mae: 7437.8994 \n", "Epoch 14/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7654.2896 - mae: 7654.2896 \n", "Epoch 15/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7479.7065 - mae: 7479.7065 \n", "Epoch 16/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 3ms/step - loss: 7289.2974 - mae: 7289.2974\n", "Epoch 17/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7616.7720 - mae: 7616.7720 \n", "Epoch 18/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7149.0117 - mae: 7149.0117 \n", "Epoch 19/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 3ms/step - loss: 7418.2275 - mae: 7418.2275\n", "Epoch 20/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7445.8247 - mae: 7445.8247 \n", "Epoch 21/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7488.9878 - mae: 7488.9878 \n", "Epoch 22/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7619.3013 - mae: 7619.3013 \n", "Epoch 23/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7414.7334 - mae: 7414.7334 \n", "Epoch 24/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7522.5991 - mae: 7522.5991 \n", "Epoch 25/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7606.9722 - mae: 7606.9722 \n", "Epoch 26/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7541.7812 - mae: 7541.7812 \n", "Epoch 27/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7664.5430 - mae: 7664.5430 \n", "Epoch 28/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7676.5708 - mae: 7676.5708 \n", "Epoch 29/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7409.3540 - mae: 7409.3540 \n", "Epoch 30/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 3ms/step - loss: 7688.6470 - mae: 7688.6470\n", "Epoch 31/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7703.0620 - mae: 7703.0620 \n", "Epoch 32/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 3ms/step - loss: 7551.4751 - mae: 7551.4751\n", "Epoch 33/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7642.3657 - mae: 7642.3657 \n", "Epoch 34/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7537.1924 - mae: 7537.1924 \n", "Epoch 35/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 3ms/step - loss: 7384.8726 - mae: 7384.8726\n", "Epoch 36/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7612.0068 - mae: 7612.0068 \n", "Epoch 37/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7639.7227 - mae: 7639.7227 \n", "Epoch 38/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7401.2285 - mae: 7401.2285 \n", "Epoch 39/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7490.0898 - mae: 7490.0898 \n", "Epoch 40/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7292.1812 - mae: 7292.1812\n", "Epoch 41/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7552.6685 - mae: 7552.6685 \n", "Epoch 42/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7332.4858 - mae: 7332.4858 \n", "Epoch 43/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7441.8887 - mae: 7441.8887 \n", "Epoch 44/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7320.3501 - mae: 7320.3501 \n", "Epoch 45/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7540.4487 - mae: 7540.4487 \n", "Epoch 46/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7436.6792 - mae: 7436.6792 \n", "Epoch 47/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7510.3140 - mae: 7510.3140 \n", "Epoch 48/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 3ms/step - loss: 7405.3877 - mae: 7405.3877\n", "Epoch 49/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7531.7368 - mae: 7531.7368 \n", "Epoch 50/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 1ms/step - loss: 7464.8989 - mae: 7464.8989 \n", "Epoch 51/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7419.5244 - mae: 7419.5244 \n", "Epoch 52/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7204.8408 - mae: 7204.8408 \n", "Epoch 53/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7371.6587 - mae: 7371.6587 \n", "Epoch 54/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7426.1885 - mae: 7426.1885 \n", "Epoch 55/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7416.7393 - mae: 7416.7393 \n", "Epoch 56/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 3ms/step - loss: 7473.5083 - mae: 7473.5083\n", "Epoch 57/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7271.3296 - mae: 7271.3296 \n", "Epoch 58/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 3ms/step - loss: 7424.8555 - mae: 7424.8555\n", "Epoch 59/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 1ms/step - loss: 7421.9839 - mae: 7421.9839 \n", "Epoch 60/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7401.8750 - mae: 7401.8750 \n", "Epoch 61/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7474.1250 - mae: 7474.1250\n", "Epoch 62/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 1ms/step - loss: 7404.3452 - mae: 7404.3452 \n", "Epoch 63/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 1ms/step - loss: 7403.3320 - mae: 7403.3320 \n", "Epoch 64/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 3ms/step - loss: 7441.0952 - mae: 7441.0952\n", "Epoch 65/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 1ms/step - loss: 7395.0156 - mae: 7395.0156 \n", "Epoch 66/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7368.3682 - mae: 7368.3682 \n", "Epoch 67/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7443.2178 - mae: 7443.2178 \n", "Epoch 68/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7435.1514 - mae: 7435.1514 \n", "Epoch 69/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 3ms/step - loss: 7179.0366 - mae: 7179.0366\n", "Epoch 70/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 1ms/step - loss: 7159.8804 - mae: 7159.8804 \n", "Epoch 71/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 1ms/step - loss: 7410.2749 - mae: 7410.2749 \n", "Epoch 72/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 3ms/step - loss: 7251.6055 - mae: 7251.6055\n", "Epoch 73/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7450.9336 - mae: 7450.9336 \n", "Epoch 74/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7075.0845 - mae: 7075.0845 \n", "Epoch 75/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7452.1372 - mae: 7452.1372 \n", "Epoch 76/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 1ms/step - loss: 7193.1670 - mae: 7193.1670 \n", "Epoch 77/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7300.4683 - mae: 7300.4683 \n", "Epoch 78/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 1ms/step - loss: 7151.2681 - mae: 7151.2681 \n", "Epoch 79/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 3ms/step - loss: 7513.1768 - mae: 7513.1768\n", "Epoch 80/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7357.8169 - mae: 7357.8169\n", "Epoch 81/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7282.6514 - mae: 7282.6514 \n", "Epoch 82/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 1ms/step - loss: 7118.7646 - mae: 7118.7646 \n", "Epoch 83/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 3ms/step - loss: 7143.0811 - mae: 7143.0811\n", "Epoch 84/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7306.7041 - mae: 7306.7041 \n", "Epoch 85/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7391.3711 - mae: 7391.3711 \n", "Epoch 86/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 6875.3101 - mae: 6875.3101 \n", "Epoch 87/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7368.6553 - mae: 7368.6553\n", "Epoch 88/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 1ms/step - loss: 7128.4619 - mae: 7128.4619 \n", "Epoch 89/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7393.0898 - mae: 7393.0898 \n", "Epoch 90/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 3ms/step - loss: 7041.3721 - mae: 7041.3721\n", "Epoch 91/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 1ms/step - loss: 7196.9800 - mae: 7196.9800 \n", "Epoch 92/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 1ms/step - loss: 7409.9585 - mae: 7409.9585 \n", "Epoch 93/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7429.2739 - mae: 7429.2739\n", "Epoch 94/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7132.4604 - mae: 7132.4604 \n", "Epoch 95/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7426.8765 - mae: 7426.8765 \n", "Epoch 96/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7318.8564 - mae: 7318.8564\n", "Epoch 97/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7368.2144 - mae: 7368.2144 \n", "Epoch 98/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7225.3174 - mae: 7225.3174 \n", "Epoch 99/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 1ms/step - loss: 7369.0454 - mae: 7369.0454 \n", "Epoch 100/100\n", "\u001b[1m34/34\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 2ms/step - loss: 7038.4531 - mae: 7038.4531 \n"]}, {"data": {"text/plain": ["<keras.src.callbacks.history.History at 0x1aa93ec1160>"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["# Set random seed\n", "tf.random.set_seed(42)\n", "\n", "# Create a new model (same as model)\n", "insurance_model = tf.keras.Sequential([\n", "  tf.keras.layers.Dense(1),\n", "  tf.keras.layers.Dense(1)\n", "])\n", "\n", "# Compile the model\n", "insurance_model.compile(loss=tf.keras.losses.mae,\n", "                        optimizer=tf.keras.optimizers.SGD(),\n", "                        metrics=['mae'])\n", "\n", "# Fit the model\n", "insurance_model.fit(X_train, y_train, epochs=100)\n", "     "]}, {"cell_type": "code", "execution_count": 18, "id": "35dac797", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[1m9/9\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 4ms/step - loss: 7968.8413 - mae: 7968.8413  \n"]}, {"data": {"text/plain": ["[7968.84130859375, 7968.84130859375]"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["# Check the results of the insurance model\n", "insurance_model.evaluate(X_test, y_test)"]}, {"cell_type": "markdown", "id": "74b1fa04", "metadata": {}, "source": ["- Increasing the number of layers (2 -> 3).\n", "- Increasing the number of units in each layer (except for the output layer).\n", "- Changing the optimizer (from SGD to Adam)."]}, {"cell_type": "code", "execution_count": 23, "id": "b0e24dcb", "metadata": {}, "outputs": [], "source": ["# Set random seed\n", "tf.random.set_seed(42)\n", "\n", "# Add an extra layer and increase number of units\n", "insurance_model_1 = tf.keras.Sequential([\n", "  tf.keras.layers.Dense(100), # 100 units\n", "  tf.keras.layers.Dense(100), # 10 units\n", "  tf.keras.layers.Dense(1) # 1 unit (important for output layer)\n", "])\n", "\n", "# Compile the model\n", "insurance_model_1.compile(loss=tf.keras.losses.mae,\n", "                          optimizer=tf.keras.optimizers.<PERSON>(), # <PERSON> works but SGD doesn't \n", "                          metrics=['mae'])\n", "\n", "# Fit the model and save the history (we can plot this)\n", "history = insurance_model_1.fit(X_train, y_train, epochs=100, verbose=0)"]}, {"cell_type": "code", "execution_count": 24, "id": "f0cf7d51", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[1m9/9\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 6ms/step - loss: 3463.1614 - mae: 3463.1614  \n"]}, {"data": {"text/plain": ["[3463.161376953125, 3463.161376953125]"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["# Evaluate our larger model\n", "insurance_model_1.evaluate(X_test, y_test)"]}, {"cell_type": "code", "execution_count": 25, "id": "527cd18b", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Plot history (also known as a loss curve)\n", "pd.<PERSON><PERSON><PERSON><PERSON>(history.history).plot()\n", "plt.ylabel(\"loss\")\n", "plt.xlabel(\"epochs\");"]}, {"cell_type": "code", "execution_count": 26, "id": "4013a24e", "metadata": {}, "outputs": [], "source": ["# Try training for a little longer (100 more epochs)\n", "history_2 = insurance_model_1.fit(X_train, y_train, epochs=100, verbose=0)"]}, {"cell_type": "code", "execution_count": 28, "id": "08900bce-f421-41fb-b64e-777ae8a7c807", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Plot history (also known as a loss curve)\n", "pd.DataFrame(history_2.history).plot()\n", "plt.ylabel(\"loss\")\n", "plt.xlabel(\"epochs\");"]}, {"cell_type": "code", "execution_count": 29, "id": "7612a900", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[1m9/9\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 9ms/step - loss: 3168.3669 - mae: 3168.3669 \n"]}, {"data": {"text/plain": ["(3168.366943359375, 3168.366943359375)"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["# Evaluate the model trained for 200 total epochs\n", "insurance_model_2_loss, insurance_model_2_mae = insurance_model_1.evaluate(X_test, y_test)\n", "insurance_model_2_loss, insurance_model_2_mae"]}, {"cell_type": "code", "execution_count": 30, "id": "5268c2a9", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import tensorflow as tf\n", "\n", "# Read in the insurance dataset\n", "insurance = pd.read_csv(\"insurance.csv\")"]}, {"cell_type": "code", "execution_count": 33, "id": "5d2f3475", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>age</th>\n", "      <th>sex</th>\n", "      <th>bmi</th>\n", "      <th>children</th>\n", "      <th>smoker</th>\n", "      <th>region</th>\n", "      <th>charges</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>19</td>\n", "      <td>female</td>\n", "      <td>27.900</td>\n", "      <td>0</td>\n", "      <td>yes</td>\n", "      <td>southwest</td>\n", "      <td>16884.92400</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>18</td>\n", "      <td>male</td>\n", "      <td>33.770</td>\n", "      <td>1</td>\n", "      <td>no</td>\n", "      <td>southeast</td>\n", "      <td>1725.55230</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>28</td>\n", "      <td>male</td>\n", "      <td>33.000</td>\n", "      <td>3</td>\n", "      <td>no</td>\n", "      <td>southeast</td>\n", "      <td>4449.46200</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>33</td>\n", "      <td>male</td>\n", "      <td>22.705</td>\n", "      <td>0</td>\n", "      <td>no</td>\n", "      <td>northwest</td>\n", "      <td>21984.47061</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>32</td>\n", "      <td>male</td>\n", "      <td>28.880</td>\n", "      <td>0</td>\n", "      <td>no</td>\n", "      <td>northwest</td>\n", "      <td>3866.85520</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   age     sex     bmi  children smoker     region      charges\n", "0   19  female  27.900         0    yes  southwest  16884.92400\n", "1   18    male  33.770         1     no  southeast   1725.55230\n", "2   28    male  33.000         3     no  southeast   4449.46200\n", "3   33    male  22.705         0     no  northwest  21984.47061\n", "4   32    male  28.880         0     no  northwest   3866.85520"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["# Check out the data\n", "insurance.head()"]}, {"cell_type": "code", "execution_count": 34, "id": "95db0bcd", "metadata": {}, "outputs": [], "source": ["from sklearn.compose import make_column_transformer\n", "from sklearn.preprocessing import MinMaxScaler, OneHotEncoder\n", "\n", "# Create column transformer (this will help us normalize/preprocess our data)\n", "ct = make_column_transformer(\n", "    (MinMaxScaler(), [\"age\", \"bmi\", \"children\"]), # get all values between 0 and 1\n", "    (OneHotEncoder(handle_unknown=\"ignore\"), [\"sex\", \"smoker\", \"region\"])\n", ")\n", "\n", "# Create X & y\n", "X = insurance.drop(\"charges\", axis=1)\n", "y = insurance[\"charges\"]\n", "\n", "# Build our train and test sets (use random state to ensure same split as before)\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)\n", "\n", "# Fit column transformer on the training data only (doing so on test data would result in data leakage)\n", "ct.fit(X_train)\n", "\n", "# Transform training and test data with normalization (MinMaxScalar) and one hot encoding (OneHotEncoder)\n", "X_train_normal = ct.transform(X_train)\n", "X_test_normal = ct.transform(X_test)"]}, {"cell_type": "code", "execution_count": 35, "id": "01238b13", "metadata": {}, "outputs": [{"data": {"text/plain": ["age                19\n", "sex            female\n", "bmi              27.9\n", "children            0\n", "smoker            yes\n", "region      southwest\n", "Name: 0, dtype: object"]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["# Non-normalized and non-one-hot encoded data example\n", "X_train.loc[0]"]}, {"cell_type": "code", "execution_count": 36, "id": "4aeced6a", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([0.60869565, 0.10734463, 0.4       , 1.        , 0.        ,\n", "       1.        , 0.        , 0.        , 1.        , 0.        ,\n", "       0.        ])"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["# Normalized and one-hot encoded example\n", "X_train_normal[0]"]}, {"cell_type": "code", "execution_count": 37, "id": "906b349b", "metadata": {}, "outputs": [{"data": {"text/plain": ["((1070, 11), (1070, 6))"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "# Notice the normalized/one-hot encoded shape is larger because of the extra columns\n", "X_train_normal.shape, X_train.shape"]}, {"cell_type": "code", "execution_count": 40, "id": "2a743ecf", "metadata": {}, "outputs": [{"data": {"text/plain": ["<keras.src.callbacks.history.History at 0x1aa9680e8b0>"]}, "execution_count": 40, "metadata": {}, "output_type": "execute_result"}], "source": ["# Set random seed\n", "tf.random.set_seed(42)\n", "\n", "# Build the model (3 layers, 100, 10, 1 units)\n", "insurance_model_3 = tf.keras.Sequential([\n", "  tf.keras.layers.Dense(100),\n", "  tf.keras.layers.Dense(100),\n", "  tf.keras.layers.Dense(1)\n", "])\n", "\n", "# Compile the model\n", "insurance_model_3.compile(loss=tf.keras.losses.mae,\n", "                          optimizer=tf.keras.optimizers.<PERSON>(),\n", "                          metrics=['mae'])\n", "\n", "# Fit the model for 200 epochs (same as insurance_model_2)\n", "insurance_model_3.fit(X_train_normal, y_train, epochs=200, verbose=0) "]}, {"cell_type": "code", "execution_count": 41, "id": "df52c479", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[1m9/9\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 3ms/step - loss: 3159.8416 - mae: 3159.8416  \n"]}], "source": ["insurance_model_3_loss, insurance_model_3_mae = insurance_model_3.evaluate(X_test_normal, y_test)"]}, {"cell_type": "code", "execution_count": 42, "id": "64bdf319", "metadata": {}, "outputs": [{"data": {"text/plain": ["(3168.366943359375, 3159.841552734375)"]}, "execution_count": 42, "metadata": {}, "output_type": "execute_result"}], "source": ["# Compare modelling results from non-normalized data and normalized data\n", "insurance_model_2_mae, insurance_model_3_mae"]}, {"cell_type": "code", "execution_count": 43, "id": "6c9ffa0f", "metadata": {}, "outputs": [{"data": {"text/plain": ["<keras.src.callbacks.history.History at 0x1aa99414e20>"]}, "execution_count": 43, "metadata": {}, "output_type": "execute_result"}], "source": ["insurance_model_3.fit(X_train_normal, y_train, epochs=200, verbose=0) "]}, {"cell_type": "code", "execution_count": 44, "id": "030413a7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[1m9/9\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 4ms/step - loss: 3160.7561 - mae: 3160.7561 \n"]}], "source": ["insurance_model_31_loss, insurance_model_31_mae = insurance_model_3.evaluate(X_test_normal, y_test)"]}, {"cell_type": "code", "execution_count": 45, "id": "c16f0360", "metadata": {}, "outputs": [{"data": {"text/plain": ["(3159.841552734375, 3160.756103515625)"]}, "execution_count": 45, "metadata": {}, "output_type": "execute_result"}], "source": ["insurance_model_3_mae, insurance_model_31_mae"]}, {"cell_type": "code", "execution_count": null, "id": "99c139fa-75ee-4dc2-a9c9-710a740d5ef0", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "genai", "language": "python", "name": "genai"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.7"}}, "nbformat": 4, "nbformat_minor": 5}